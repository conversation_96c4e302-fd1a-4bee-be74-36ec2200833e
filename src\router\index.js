import { createRouter, createWebHistory } from 'vue-router'
import Login from '../components/Login.vue'
import Home from '../components/Home.vue'
import UserProfile from '../components/UserProfile.vue'
import Info from '../components/info.vue'
import FaceManagement from '../components/FaceManagement.vue'
import CourseManagement from '../components/CourseManagement.vue'
import AttendanceCheck from '../components/AttendanceCheck.vue'
import LeaveApplication from '../components/LeaveApplication.vue'
import AttendanceStatistics from '../components/AttendanceStatistics.vue'
import AttendanceRecords from '../components/AttendanceRecords.vue'
import SafetyMonitoring from '../components/SafetyMonitoring.vue'
import { authUtils } from '../utils/api'

const routes = [
  {
    path: '/',
    name: 'Login',
    component: Login
  },
  {
    path: '/home',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'UserProfile',
    component: UserProfile,
    meta: { requiresAuth: true }
  },
  {
    path: '/info',
    name: 'Info',
    component: Info,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/profile/:id',
    name: 'UserProfileDetail',
    component: UserProfile,
    meta: { requiresAuth: true }
  },
  {
    path: '/face-management',
    name: 'FaceManagement',
    component: FaceManagement,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/courses',
    name: 'CourseManagement',
    component: CourseManagement,
    meta: { requiresAuth: true, requiresTeacher: true }
  },
  {
    path: '/attendance',
    name: 'AttendanceCheck',
    component: AttendanceCheck,
    meta: { requiresAuth: true }
  },
  {
    path: '/leave',
    name: 'LeaveApplication',
    component: LeaveApplication,
    meta: { requiresAuth: true }
  },
  {
    path: '/statistics',
    name: 'AttendanceStatistics',
    component: AttendanceStatistics,
    meta: { requiresAuth: true, requiresTeacher: true }
  },
  {
    path: '/records',
    name: 'AttendanceRecords',
    component: AttendanceRecords,
    meta: { requiresAuth: true, requiresTeacher: true }
  },
  {
    path: '/safety',
    name: 'SafetyMonitoring',
    component: SafetyMonitoring,
    meta: { requiresAuth: true, requiresAdmin: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('authToken')

  if (to.meta.requiresAuth && !token) {
    // 需要认证但没有token，跳转到登录页
    next('/')
  } else if (to.path === '/' && token) {
    // 已登录用户访问登录页，跳转到首页
    next('/home')
  } else if (to.meta.requiresAdmin) {
    // 需要管理员权限的页面
    const currentUser = authUtils.getCurrentUser()
    console.log('路由守卫检查管理员权限:', currentUser)

    if (!currentUser) {
      console.log('未找到用户信息，跳转到登录页')
      next('/')
    } else if (!currentUser.role) {
      console.log('用户信息缺少角色字段，需要重新登录')
      const shouldRelogin = confirm('用户信息不完整，需要重新登录以获取完整权限信息。是否现在重新登录？')
      if (shouldRelogin) {
        authUtils.clearAuth()
        next('/')
      } else {
        next('/home')
      }
    } else if (currentUser.role !== 'admin') {
      console.log('用户角色不是管理员:', currentUser.role)
      alert('权限不足，只有管理员可以访问此页面')
      next('/home')
    } else {
      console.log('管理员权限验证通过')
      next()
    }
  } else if (to.meta.requiresTeacher) {
    // 需要教师权限的页面（管理员和教师都可以访问）
    const currentUser = authUtils.getCurrentUser()
    console.log('路由守卫检查教师权限:', currentUser)

    if (!currentUser) {
      console.log('未找到用户信息，跳转到登录页')
      next('/')
    } else if (!currentUser.role) {
      console.log('用户信息缺少角色字段，需要重新登录')
      const shouldRelogin = confirm('用户信息不完整，需要重新登录以获取完整权限信息。是否现在重新登录？')
      if (shouldRelogin) {
        authUtils.clearAuth()
        next('/')
      } else {
        next('/home')
      }
    } else if (!['admin', 'teacher'].includes(currentUser.role)) {
      console.log('用户角色不是管理员或教师:', currentUser.role)
      alert('权限不足，只有管理员和教师可以访问此页面')
      next('/home')
    } else {
      console.log('教师权限验证通过')
      next()
    }
  } else {
    next()
  }
})

export default router