<template>
  <div class="face-capture">
    <div class="capture-container">
      <!-- 标题 -->
      <div class="capture-header">
        <h3>人脸信息采集</h3>
        <p>请选择拍摄或上传人脸照片</p>
      </div>

      <!-- 操作选项 -->
      <div class="capture-options">
        <div class="option-tabs">
          <button 
            :class="['tab-btn', { active: activeTab === 'camera' }]"
            @click="switchTab('camera')"
          >
            <camera-outlined />
            摄像头拍摄
          </button>
          <button 
            :class="['tab-btn', { active: activeTab === 'upload' }]"
            @click="switchTab('upload')"
          >
            <upload-outlined />
            文件上传
          </button>
        </div>
      </div>

      <!-- 摄像头拍摄区域 -->
      <div v-if="activeTab === 'camera'" class="camera-section">
        <div class="camera-container">
          <video 
            ref="videoRef" 
            v-show="!capturedImage && cameraActive"
            class="camera-video"
            autoplay
            muted
          ></video>
          
          <canvas 
            ref="canvasRef" 
            v-show="false"
            class="camera-canvas"
          ></canvas>

          <div v-if="capturedImage" class="captured-preview">
            <img :src="capturedImage" alt="拍摄的照片" class="captured-image" />
          </div>

          <div v-if="!cameraActive && !capturedImage" class="camera-placeholder">
            <camera-outlined class="placeholder-icon" />
            <p>点击开启摄像头</p>
          </div>
        </div>

        <div class="camera-controls">
          <button 
            v-if="!cameraActive && !capturedImage"
            @click="startCamera"
            class="control-btn primary"
            :disabled="loading"
          >
            <camera-outlined />
            开启摄像头
          </button>

          <template v-if="cameraActive && !capturedImage">
            <button @click="capturePhoto" class="control-btn primary">
              <camera-outlined />
              拍摄照片
            </button>
            <button @click="stopCamera" class="control-btn secondary">
              <stop-outlined />
              关闭摄像头
            </button>
          </template>

          <template v-if="capturedImage">
            <button @click="uploadCapturedImage" class="control-btn success" :disabled="uploading">
              <upload-outlined />
              {{ uploading ? '上传中...' : '确认上传' }}
            </button>
            <button @click="retakePhoto" class="control-btn secondary">
              <redo-outlined />
              重新拍摄
            </button>
          </template>
        </div>
      </div>

      <!-- 文件上传区域 -->
      <div v-if="activeTab === 'upload'" class="upload-section">
        <div class="upload-area" @click="triggerFileInput" @dragover.prevent @drop="handleFileDrop">
          <input 
            ref="fileInputRef"
            type="file"
            accept="image/*"
            @change="handleFileSelect"
            style="display: none"
          />
          
          <div v-if="!selectedFile" class="upload-placeholder">
            <upload-outlined class="upload-icon" />
            <p class="upload-text">点击选择或拖拽人脸照片到此处</p>
            <p class="upload-hint">支持 JPG、PNG、GIF、WEBP 格式，最大 10MB</p>
          </div>

          <div v-if="selectedFile" class="file-preview">
            <img :src="filePreview" alt="选择的照片" class="preview-image" />
            <div class="file-info">
              <p class="file-name">{{ selectedFile.name }}</p>
              <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
            </div>
          </div>
        </div>

        <div v-if="selectedFile" class="upload-controls">
          <button @click="uploadSelectedFile" class="control-btn success" :disabled="uploading">
            <upload-outlined />
            {{ uploading ? '上传中...' : '确认上传' }}
          </button>
          <button @click="clearSelectedFile" class="control-btn secondary">
            <delete-outlined />
            清除选择
          </button>
        </div>
      </div>

      <!-- 当前人脸信息 -->
      <div v-if="currentFaceInfo" class="current-face">
        <h4>当前人脸信息</h4>
        <div class="current-face-preview">
          <img :src="getFaceUrl(currentFaceInfo)" alt="当前人脸" class="current-face-image" />
          <button
            v-if="canDeleteFace"
            @click="deleteFace"
            class="delete-face-btn"
            :disabled="loading"
          >
            <delete-outlined />
            删除人脸
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { 
  CameraOutlined, 
  UploadOutlined, 
  StopOutlined, 
  RedoOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { authAPI, authUtils } from '../utils/api';

// Props
const props = defineProps({
  userId: {
    type: [String, Number],
    default: null
  },
  currentFaceInfo: {
    type: String,
    default: null
  }
});

// Emits
const emit = defineEmits(['face-updated', 'face-deleted']);

// 响应式数据
const activeTab = ref('camera');
const cameraActive = ref(false);
const capturedImage = ref(null);
const selectedFile = ref(null);
const filePreview = ref(null);
const loading = ref(false);
const uploading = ref(false);

// 引用
const videoRef = ref(null);
const canvasRef = ref(null);
const fileInputRef = ref(null);

// 摄像头流
let mediaStream = null;

// 计算属性：检查是否可以删除人脸
const canDeleteFace = computed(() => {
  const currentUser = authUtils.getCurrentUser();
  if (!currentUser) return false;

  // 管理员可以删除任何人的人脸
  if (currentUser.role === 'admin') {
    return true;
  }

  // 普通用户只能删除自己的人脸
  return props.userId === 'current' || props.userId === currentUser.id;
});

// 切换标签
const switchTab = (tab) => {
  if (tab !== activeTab.value) {
    activeTab.value = tab;
    if (tab === 'upload') {
      stopCamera();
    }
  }
};

// 开启摄像头
const startCamera = async () => {
  try {
    loading.value = true;
    mediaStream = await navigator.mediaDevices.getUserMedia({ 
      video: { 
        width: { ideal: 640 },
        height: { ideal: 480 },
        facingMode: 'user'
      } 
    });
    
    if (videoRef.value) {
      videoRef.value.srcObject = mediaStream;
      cameraActive.value = true;
    }
  } catch (error) {
    console.error('摄像头启动失败:', error);
    message.error('摄像头启动失败，请检查设备权限');
  } finally {
    loading.value = false;
  }
};

// 关闭摄像头
const stopCamera = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach(track => track.stop());
    mediaStream = null;
  }
  cameraActive.value = false;
  capturedImage.value = null;
};

// 拍摄照片
const capturePhoto = () => {
  if (!videoRef.value || !canvasRef.value) return;
  
  const video = videoRef.value;
  const canvas = canvasRef.value;
  const context = canvas.getContext('2d');
  
  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;
  
  context.drawImage(video, 0, 0);
  capturedImage.value = canvas.toDataURL('image/jpeg', 0.8);
  
  stopCamera();
};

// 重新拍摄
const retakePhoto = () => {
  capturedImage.value = null;
  startCamera();
};

// 上传拍摄的图片
const uploadCapturedImage = async () => {
  if (!capturedImage.value) return;

  try {
    uploading.value = true;

    // 将base64转换为文件
    const response = await fetch(capturedImage.value);
    const blob = await response.blob();
    const file = new File([blob], 'captured_face.jpg', { type: 'image/jpeg' });

    const formData = new FormData();
    formData.append('face', file);

    let result;
    if (props.userId && props.userId !== 'current') {
      // 管理员为指定用户上传人脸
      console.log(`管理员为用户 ${props.userId} 上传人脸`);
      result = await authAPI.adminUploadUserFace(props.userId, formData);
    } else {
      // 用户为自己上传人脸（包括userId为'current'的情况）
      console.log('用户为自己上传人脸');
      result = await authAPI.uploadFace(formData);
    }

    message.success('人脸照片上传成功');
    emit('face-updated', result.data.face_info);

    // 清理状态
    capturedImage.value = null;

  } catch (error) {
    console.error('上传失败:', error);
    message.error(error.response?.data?.error || '上传失败');
  } finally {
    uploading.value = false;
  }
};

// 触发文件选择
const triggerFileInput = () => {
  fileInputRef.value?.click();
};

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (file) {
    processSelectedFile(file);
  }
};

// 处理文件拖拽
const handleFileDrop = (event) => {
  event.preventDefault();
  const file = event.dataTransfer.files[0];
  if (file) {
    processSelectedFile(file);
  }
};

// 处理选择的文件
const processSelectedFile = (file) => {
  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    message.error('请选择有效的图片文件（JPG、PNG、GIF、WEBP）');
    return;
  }
  
  // 验证文件大小（10MB）
  if (file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过10MB');
    return;
  }
  
  selectedFile.value = file;
  
  // 创建预览
  const reader = new FileReader();
  reader.onload = (e) => {
    filePreview.value = e.target.result;
  };
  reader.readAsDataURL(file);
};

// 上传选择的文件
const uploadSelectedFile = async () => {
  if (!selectedFile.value) return;

  try {
    uploading.value = true;

    const formData = new FormData();
    formData.append('face', selectedFile.value);

    let result;
    if (props.userId && props.userId !== 'current') {
      // 管理员为指定用户上传人脸
      console.log(`管理员为用户 ${props.userId} 上传人脸文件`);
      result = await authAPI.adminUploadUserFace(props.userId, formData);
    } else {
      // 用户为自己上传人脸（包括userId为'current'的情况）
      console.log('用户为自己上传人脸文件');
      result = await authAPI.uploadFace(formData);
    }

    message.success('人脸照片上传成功');
    emit('face-updated', result.data.face_info);

    // 清理状态
    clearSelectedFile();

  } catch (error) {
    console.error('上传失败:', error);
    message.error(error.response?.data?.error || '上传失败');
  } finally {
    uploading.value = false;
  }
};

// 清除选择的文件
const clearSelectedFile = () => {
  selectedFile.value = null;
  filePreview.value = null;
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }
};

// 删除人脸信息
const deleteFace = async () => {
  try {
    loading.value = true;

    // 获取当前用户信息
    const currentUser = authUtils.getCurrentUser();

    // 如果是管理员且删除的不是自己的人脸，使用管理员删除API
    if (currentUser?.role === 'admin' && props.userId !== 'current' && props.userId !== currentUser.id) {
      await authAPI.adminDeleteUserFace(props.userId);
    } else {
      // 普通用户删除自己的人脸，或管理员删除自己的人脸
      await authAPI.deleteFace();
    }

    message.success('人脸信息删除成功');
    emit('face-deleted');
  } catch (error) {
    console.error('删除失败:', error);
    message.error(error.response?.data?.error || '删除失败');
  } finally {
    loading.value = false;
  }
};

// 获取人脸图片URL
const getFaceUrl = (filename) => {
  return authAPI.getFaceUrl(filename);
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 组件卸载时清理资源
onUnmounted(() => {
  stopCamera();
});
</script>

<style scoped>
.face-capture {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.capture-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.capture-header {
  text-align: center;
  margin-bottom: 24px;
}

.capture-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

.capture-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 选项卡样式 */
.capture-options {
  margin-bottom: 24px;
}

.option-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  color: #666;
}

.tab-btn.active {
  background: white;
  color: #1890ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
  color: #1890ff;
}

/* 摄像头区域样式 */
.camera-section {
  margin-bottom: 24px;
}

.camera-container {
  position: relative;
  width: 100%;
  height: 300px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.captured-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captured-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.camera-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

/* 上传区域样式 */
.upload-section {
  margin-bottom: 24px;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 12px;
}

.upload-text {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.upload-hint {
  margin: 0;
  font-size: 12px;
  color: #999;
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-info {
  text-align: center;
}

.file-name {
  margin: 0 0 4px 0;
  font-weight: 500;
  color: #333;
}

.file-size {
  margin: 0;
  font-size: 12px;
  color: #999;
}

/* 控制按钮样式 */
.camera-controls,
.upload-controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
  min-width: 120px;
  justify-content: center;
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.control-btn.primary {
  background: #1890ff;
  color: white;
}

.control-btn.primary:hover:not(:disabled) {
  background: #40a9ff;
}

.control-btn.success {
  background: #52c41a;
  color: white;
}

.control-btn.success:hover:not(:disabled) {
  background: #73d13d;
}

.control-btn.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}

.control-btn.secondary:hover:not(:disabled) {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

/* 当前人脸信息样式 */
.current-face {
  border-top: 1px solid #e8e8e8;
  padding-top: 24px;
}

.current-face h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.current-face-preview {
  display: flex;
  align-items: center;
  gap: 16px;
}

.current-face-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e8e8e8;
}

.delete-face-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.delete-face-btn:hover:not(:disabled) {
  background: #ff7875;
}

.delete-face-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .capture-container {
    padding: 16px;
  }

  .camera-container {
    height: 250px;
  }

  .camera-controls,
  .upload-controls {
    flex-direction: column;
  }

  .control-btn {
    width: 100%;
  }

  .current-face-preview {
    flex-direction: column;
    align-items: flex-start;
  }

  .delete-face-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
