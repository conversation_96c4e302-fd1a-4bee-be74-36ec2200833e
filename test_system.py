#!/usr/bin/env python3
"""
课程表管理和签到系统测试脚本
用于验证系统的基本功能是否正常工作
"""

import requests
import json
import sys
from datetime import datetime, date

# 配置
BASE_URL = "http://127.0.0.1:5000/api"
TEST_USER = {
    "username": "test_admin",
    "email": "<EMAIL>", 
    "password": "test123456"
}

class SystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.user_id = None
        
    def test_connection(self):
        """测试服务器连接"""
        try:
            response = self.session.get(f"{BASE_URL}/users")
            print(f"✓ 服务器连接正常 (状态码: {response.status_code})")
            return True
        except requests.exceptions.ConnectionError:
            print("✗ 无法连接到服务器，请确保后端服务正在运行")
            return False
        except Exception as e:
            print(f"✗ 连接测试失败: {e}")
            return False
    
    def test_login(self):
        """测试登录功能"""
        try:
            # 尝试使用默认管理员账户登录
            login_data = {
                "login": "admin",
                "password": "123456"  # 默认密码
            }
            
            response = self.session.post(f"{BASE_URL}/login", json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('token')
                self.user_id = data.get('user', {}).get('id')
                
                # 设置认证头
                self.session.headers.update({
                    'Authorization': f'Bearer {self.token}'
                })
                
                print(f"✓ 登录成功 (用户ID: {self.user_id})")
                return True
            else:
                error_data = response.json()
                print(f"✗ 登录失败: {error_data.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"✗ 登录测试失败: {e}")
            return False
    
    def test_course_management(self):
        """测试课程管理功能"""
        try:
            # 测试获取课程列表
            response = self.session.get(f"{BASE_URL}/courses")
            if response.status_code == 200:
                courses = response.json().get('courses', [])
                print(f"✓ 获取课程列表成功 (共 {len(courses)} 门课程)")
            else:
                print(f"✗ 获取课程列表失败: {response.status_code}")
                return False
            
            # 测试创建课程
            test_course = {
                "course_name": "测试课程",
                "course_code": "TEST001",
                "teacher_id": self.user_id,
                "classroom": "测试教室",
                "start_time": "09:00",
                "end_time": "10:40",
                "day_of_week": 1,
                "department": "测试部门",
                "semester": "2024-2025-1",
                "academic_year": "2024-2025",
                "credits": 2.0,
                "course_type": "测试",
                "weeks": "1-16",
                "description": "这是一个测试课程"
            }
            
            response = self.session.post(f"{BASE_URL}/courses", json=test_course)
            if response.status_code == 201:
                course_data = response.json()
                course_id = course_data.get('course_id')
                print(f"✓ 创建课程成功 (课程ID: {course_id})")
                
                # 测试删除课程
                if course_id:
                    delete_response = self.session.delete(f"{BASE_URL}/courses/{course_id}")
                    if delete_response.status_code == 200:
                        print("✓ 删除课程成功")
                    else:
                        print(f"✗ 删除课程失败: {delete_response.status_code}")
                        
            else:
                error_data = response.json()
                print(f"✗ 创建课程失败: {error_data.get('error', '未知错误')}")
                return False
                
            return True
            
        except Exception as e:
            print(f"✗ 课程管理测试失败: {e}")
            return False
    
    def test_attendance_system(self):
        """测试签到系统功能"""
        try:
            # 测试获取签到记录
            today = date.today().strftime('%Y-%m-%d')
            response = self.session.get(f"{BASE_URL}/attendance/records?start_date={today}&end_date={today}")
            
            if response.status_code == 200:
                records = response.json().get('records', [])
                print(f"✓ 获取签到记录成功 (今日 {len(records)} 条记录)")
            else:
                print(f"✗ 获取签到记录失败: {response.status_code}")
                return False
            
            # 测试获取签到统计
            response = self.session.get(f"{BASE_URL}/attendance/statistics")
            if response.status_code == 200:
                stats = response.json()
                status_stats = stats.get('status_statistics', [])
                student_stats = stats.get('student_statistics', [])
                print(f"✓ 获取签到统计成功 (状态统计: {len(status_stats)} 项, 学生统计: {len(student_stats)} 人)")
            else:
                print(f"✗ 获取签到统计失败: {response.status_code}")
                return False
                
            return True
            
        except Exception as e:
            print(f"✗ 签到系统测试失败: {e}")
            return False
    
    def test_leave_system(self):
        """测试请假系统功能"""
        try:
            # 测试获取请假申请列表
            response = self.session.get(f"{BASE_URL}/leave/applications")
            
            if response.status_code == 200:
                applications = response.json().get('applications', [])
                print(f"✓ 获取请假申请成功 (共 {len(applications)} 条申请)")
            else:
                print(f"✗ 获取请假申请失败: {response.status_code}")
                return False
                
            return True
            
        except Exception as e:
            print(f"✗ 请假系统测试失败: {e}")
            return False
    
    def test_user_management(self):
        """测试用户管理功能"""
        try:
            # 测试获取用户列表
            response = self.session.get(f"{BASE_URL}/users")
            
            if response.status_code == 200:
                users_data = response.json()
                users = users_data.get('users', [])
                print(f"✓ 获取用户列表成功 (共 {len(users)} 个用户)")
            else:
                print(f"✗ 获取用户列表失败: {response.status_code}")
                return False
                
            return True
            
        except Exception as e:
            print(f"✗ 用户管理测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 50)
        print("课程表管理和签到系统 - 功能测试")
        print("=" * 50)
        
        tests = [
            ("服务器连接", self.test_connection),
            ("用户登录", self.test_login),
            ("课程管理", self.test_course_management),
            ("签到系统", self.test_attendance_system),
            ("请假系统", self.test_leave_system),
            ("用户管理", self.test_user_management),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n[{test_name}]")
            try:
                if test_func():
                    passed += 1
                else:
                    print(f"  {test_name} 测试失败")
            except Exception as e:
                print(f"  {test_name} 测试异常: {e}")
        
        print("\n" + "=" * 50)
        print(f"测试完成: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统功能正常")
            return True
        else:
            print("⚠️  部分测试失败，请检查系统配置")
            return False

def main():
    """主函数"""
    print("启动系统测试...")
    
    tester = SystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 系统测试完成，所有功能正常工作")
        sys.exit(0)
    else:
        print("\n❌ 系统测试发现问题，请检查配置和服务状态")
        sys.exit(1)

if __name__ == "__main__":
    main()
