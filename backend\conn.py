from flask import Flask, jsonify, request
import mysql.connector
from mysql.connector import Error
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import datetime
import os
import re
from functools import wraps
from dotenv import load_dotenv
from werkzeug.utils import secure_filename
import uuid
from PIL import Image
import random
import string

# 加载环境变量
load_dotenv()

app = Flask(__name__)

# 文件上传配置
UPLOAD_FOLDER = 'uploads/avatars'
FACE_UPLOAD_FOLDER = 'uploads/faces'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
MAX_FACE_SIZE = 10 * 1024 * 1024  # 10MB for face images

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(FACE_UPLOAD_FOLDER, exist_ok=True)

# 安全配置
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-in-production')
app.config['JWT_EXPIRATION_DELTA'] = datetime.timedelta(hours=24)

# CORS配置 - 仅允许特定来源
CORS(app, origins=['http://localhost:3000', 'http://localhost:5173'],
     methods=['GET', 'POST', 'PUT', 'DELETE'],
     allow_headers=['Content-Type', 'Authorization'])

# 数据库连接配置 - 使用环境变量
db_config = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', '123456'),
    'database': os.environ.get('DB_NAME', 'security'),
    'autocommit': True
}

# 输入验证函数
def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    # 密码至少8位，包含字母和数字
    if len(password) < 8:
        return False
    if not re.search(r'[A-Za-z]', password):
        return False
    if not re.search(r'\d', password):
        return False
    return True

def validate_username(username):
    # 用户名3-20位，只能包含字母、数字和下划线
    pattern = r'^[a-zA-Z0-9_]{3,20}$'
    return re.match(pattern, username) is not None

# 文件上传相关函数
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(filename):
    """生成唯一的文件名"""
    ext = filename.rsplit('.', 1)[1].lower()
    unique_filename = f"{uuid.uuid4().hex}.{ext}"
    return unique_filename

def generate_user_face_filename(user_id, original_filename):
    """为用户生成专属的人脸文件名"""
    ext = original_filename.rsplit('.', 1)[1].lower()
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_filename = f"face_{user_id}_{timestamp}_{uuid.uuid4().hex[:8]}.{ext}"
    return unique_filename

def resize_image(image_path, max_size=(300, 300)):
    """调整图片大小"""
    try:
        with Image.open(image_path) as img:
            # 转换为RGB模式（处理RGBA等格式）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')

            # 保持宽高比缩放
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            img.save(image_path, 'JPEG', quality=85, optimize=True)
        return True
    except Exception as e:
        print(f"图片处理错误: {e}")
        return False

# JWT装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': '缺少认证token'}), 401

        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user_id = data['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'Token已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': '无效的token'}), 401

        return f(current_user_id, *args, **kwargs)
    return decorated

# 数据库连接函数
def get_db_connection():
    try:
        connection = mysql.connector.connect(**db_config)
        return connection
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None
# 用户注册API
@app.route('/api/register', methods=['POST'])
def register():
    try:
        data = request.get_json()

        # 输入验证
        if not data:
            return jsonify({'error': '请提供注册信息'}), 400

        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')

        # 验证必填字段
        if not username or not email or not password:
            return jsonify({'error': '用户名、邮箱和密码都是必填项'}), 400

        # 验证格式
        if not validate_username(username):
            return jsonify({'error': '用户名格式不正确（3-20位字母、数字或下划线）'}), 400

        if not validate_email(email):
            return jsonify({'error': '邮箱格式不正确'}), 400

        if not validate_password(password):
            return jsonify({'error': '密码至少8位，必须包含字母和数字'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor()

        # 检查用户名和邮箱是否已存在
        check_query = "SELECT id FROM user WHERE username = %s OR email = %s"
        cursor.execute(check_query, (username, email))
        existing_user = cursor.fetchone()

        if existing_user:
            return jsonify({'error': '用户名或邮箱已存在'}), 409

        # 密码哈希
        password_hash = generate_password_hash(password)

        # 插入新用户（默认角色为student）
        insert_query = """
            INSERT INTO user (username, email, password_hash, avatar, role, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        cursor.execute(insert_query, (username, email, password_hash, None, 'student', datetime.datetime.now()))

        user_id = cursor.lastrowid

        # 生成JWT token
        token = jwt.encode({
            'user_id': user_id,
            'username': username,
            'exp': datetime.datetime.utcnow() + app.config['JWT_EXPIRATION_DELTA']
        }, app.config['SECRET_KEY'], algorithm='HS256')

        # 确保token是字符串格式
        if isinstance(token, bytes):
            token = token.decode('utf-8')

        return jsonify({
            'message': '注册成功',
            'token': token,
            'user': {
                'id': user_id,
                'username': username,
                'email': email,
                'avatar': None,
                'role': 'student'
            }
        }), 201

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 用户登录API
@app.route('/api/login', methods=['POST'])
def login():
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请提供登录信息'}), 400

        login_field = data.get('login', '').strip()  # 可以是用户名或邮箱
        password = data.get('password', '')

        if not login_field or not password:
            return jsonify({'error': '请输入用户名/邮箱和密码'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 查询用户（支持用户名或邮箱登录）
        query = """
            SELECT id, username, email, password_hash, avatar, role
            FROM user
            WHERE username = %s OR email = %s
        """
        cursor.execute(query, (login_field, login_field))
        user = cursor.fetchone()

        if not user or not check_password_hash(user['password_hash'], password):
            return jsonify({'error': '用户名/邮箱或密码错误'}), 401

        # 生成JWT token
        token = jwt.encode({
            'user_id': user['id'],
            'username': user['username'],
            'exp': datetime.datetime.utcnow() + app.config['JWT_EXPIRATION_DELTA']
        }, app.config['SECRET_KEY'], algorithm='HS256')

        # 确保token是字符串格式
        if isinstance(token, bytes):
            token = token.decode('utf-8')

        return jsonify({
            'message': '登录成功',
            'token': token,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'avatar': user['avatar'],
                'role': user['role']
            }
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取用户信息API（需要认证）
@app.route('/api/user/profile', methods=['GET'])
@token_required
def get_user_profile(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)
        # 获取完整的用户信息，包括所有字段
        query = """
            SELECT id, username, email, sex, birth_date, grade, department, number, phone, role,
                   face_info, avatar, created_at, updated_at, is_active,
                   CASE
                       WHEN birth_date IS NOT NULL THEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE())
                       ELSE NULL
                   END as age
            FROM user
            WHERE id = %s
        """
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': '用户不存在'}), 404

        # 格式化日期
        if user['created_at']:
            user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if user['updated_at']:
            user['updated_at'] = user['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        if user['birth_date']:
            user['birth_date'] = user['birth_date'].strftime('%Y-%m-%d')

        # 根据角色设置标签
        if user['role'] == 'admin':
            user['tags'] = ['管理员']
        elif user['role'] == 'teacher':
            user['tags'] = ['教师']
        else:
            user['tags'] = ['学生']

        return jsonify({'user': user}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 更新用户信息API（需要认证）
@app.route('/api/user/profile', methods=['PUT'])
@token_required
def update_user_profile(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供要更新的数据'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 构建更新字段和值
        update_fields = []
        update_values = []

        # 允许更新的字段
        allowed_fields = ['email', 'sex', 'birth_date', 'grade', 'department', 'number', 'phone']

        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = %s")
                update_values.append(data[field])

        if not update_fields:
            return jsonify({'error': '没有提供有效的更新字段'}), 400

        # 检查邮箱和学号/教职号的唯一性
        if 'email' in data:
            check_query = "SELECT id FROM user WHERE email = %s AND id != %s"
            cursor.execute(check_query, (data['email'], current_user_id))
            if cursor.fetchone():
                return jsonify({'error': '该邮箱已被其他用户使用'}), 400

        if 'number' in data and data['number']:
            check_query = "SELECT id FROM user WHERE number = %s AND id != %s"
            cursor.execute(check_query, (data['number'], current_user_id))
            if cursor.fetchone():
                return jsonify({'error': '该学号/教职号已被其他用户使用'}), 400

        # 添加更新时间
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        update_values.append(current_user_id)

        # 构建并执行更新查询
        update_query = f"UPDATE user SET {', '.join(update_fields)} WHERE id = %s"
        cursor.execute(update_query, update_values)

        # 获取更新后的用户信息
        query = """
            SELECT id, username, email, sex, birth_date, grade, department, number, phone, role,
                   face_info, avatar, created_at, updated_at, is_active,
                   CASE
                       WHEN birth_date IS NOT NULL THEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE())
                       ELSE NULL
                   END as age
            FROM user
            WHERE id = %s
        """
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if user:
            # 格式化日期
            if user['created_at']:
                user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if user['updated_at']:
                user['updated_at'] = user['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            if user['birth_date']:
                user['birth_date'] = user['birth_date'].strftime('%Y-%m-%d')

            # 根据角色设置标签
            if user['role'] == 'admin':
                user['tags'] = ['管理员']
            elif user['role'] == 'teacher':
                user['tags'] = ['教师']
            else:
                user['tags'] = ['学生']

        return jsonify({
            'message': '用户信息更新成功',
            'user': user
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 验证token API
@app.route('/api/verify-token', methods=['POST'])
@token_required
def verify_token(current_user_id):
    return jsonify({'valid': True, 'user_id': current_user_id}), 200

# 头像上传API
@app.route('/api/user/avatar', methods=['POST'])
@token_required
def upload_avatar(current_user_id):
    try:
        # 检查是否有文件上传
        if 'avatar' not in request.files:
            return jsonify({'error': '请选择头像文件'}), 400

        file = request.files['avatar']

        # 检查文件名
        if file.filename == '':
            return jsonify({'error': '请选择头像文件'}), 400

        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式，请上传 PNG、JPG、JPEG、GIF 或 WEBP 格式的图片'}), 400

        # 检查文件大小
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > MAX_FILE_SIZE:
            return jsonify({'error': '文件大小不能超过5MB'}), 400

        # 生成唯一文件名
        filename = generate_unique_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)

        # 保存文件
        file.save(file_path)

        # 调整图片大小
        if not resize_image(file_path):
            os.remove(file_path)  # 删除处理失败的文件
            return jsonify({'error': '图片处理失败'}), 500

        # 更新数据库中的头像路径
        connection = get_db_connection()
        if not connection:
            os.remove(file_path)  # 删除已上传的文件
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取用户当前头像路径（用于删除旧头像）
        query = "SELECT avatar FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        old_avatar = user['avatar'] if user else None

        # 更新头像路径
        update_query = "UPDATE user SET avatar = %s WHERE id = %s"
        cursor.execute(update_query, (filename, current_user_id))

        # 删除旧头像文件（如果存在）
        if old_avatar and old_avatar != filename:
            old_file_path = os.path.join(UPLOAD_FOLDER, old_avatar)
            if os.path.exists(old_file_path):
                try:
                    os.remove(old_file_path)
                except Exception as e:
                    print(f"删除旧头像失败: {e}")

        return jsonify({
            'message': '头像上传成功',
            'avatar': filename
        }), 200

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取头像API
@app.route('/api/user/avatar/<filename>', methods=['GET'])
def get_avatar(filename):
    try:
        # 安全检查文件名
        filename = secure_filename(filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({'error': '头像文件不存在'}), 404

        # 返回文件
        from flask import send_file
        return send_file(file_path)

    except Exception as e:
        return jsonify({'error': f'获取头像失败: {str(e)}'}), 500

# 删除头像API
@app.route('/api/user/avatar', methods=['DELETE'])
@token_required
def delete_avatar(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取用户当前头像路径
        query = "SELECT avatar FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or not user['avatar']:
            return jsonify({'error': '用户没有设置头像'}), 404

        # 删除头像文件
        file_path = os.path.join(UPLOAD_FOLDER, user['avatar'])
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as e:
                print(f"删除头像文件失败: {e}")

        # 更新数据库，清除头像路径
        update_query = "UPDATE user SET avatar = NULL WHERE id = %s"
        cursor.execute(update_query, (current_user_id,))

        return jsonify({'message': '头像删除成功'}), 200

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取用户列表API（需要认证）
@app.route('/api/users', methods=['GET'])
@token_required
def get_users_list(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取所有用户信息（不包含密码）
        query = """
            SELECT id, username, email, sex, birth_date, grade, department, number, phone, role,
                   face_info, avatar, created_at, updated_at, is_active,
                   CASE
                       WHEN birth_date IS NOT NULL THEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE())
                       ELSE NULL
                   END as age
            FROM user
            WHERE is_active = 1
            ORDER BY created_at DESC
        """
        cursor.execute(query)
        users = cursor.fetchall()

        # 处理返回数据，添加标签信息
        for user in users:
            # 根据角色设置标签
            if user['role'] == 'admin':
                user['tags'] = ['管理员']
            elif user['role'] == 'teacher':
                user['tags'] = ['教师']
            else:
                user['tags'] = ['学生']

            # 格式化日期
            if user['created_at']:
                user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if user['updated_at']:
                user['updated_at'] = user['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            if user['birth_date']:
                user['birth_date'] = user['birth_date'].strftime('%Y-%m-%d')

        return jsonify({
            'users': users,
            'total': len(users)
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 人脸图片上传API
@app.route('/api/user/face', methods=['POST'])
@token_required
def upload_face(current_user_id):
    return _upload_face_for_user(current_user_id, current_user_id)

# 管理员为指定用户上传人脸API
@app.route('/api/admin/user/<int:target_user_id>/face', methods=['POST'])
@token_required
def admin_upload_face(current_user_id, target_user_id):
    # 验证管理员权限
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': '数据库连接失败'}), 500

    try:
        cursor = connection.cursor(dictionary=True)
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        admin_user = cursor.fetchone()

        if not admin_user or admin_user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以为其他用户上传人脸'}), 403

        # 检查目标用户是否存在
        query = "SELECT id FROM user WHERE id = %s"
        cursor.execute(query, (target_user_id,))
        target_user = cursor.fetchone()

        if not target_user:
            return jsonify({'error': '目标用户不存在'}), 404

    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

    return _upload_face_for_user(current_user_id, target_user_id)

# 通用人脸上传处理函数
def _upload_face_for_user(current_user_id, target_user_id):
    try:
        # 检查是否有文件上传
        if 'face' not in request.files:
            return jsonify({'error': '请选择人脸图片文件'}), 400

        file = request.files['face']

        # 检查文件名
        if file.filename == '':
            return jsonify({'error': '请选择人脸图片文件'}), 400

        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式，请上传 PNG、JPG、JPEG、GIF 或 WEBP 格式的图片'}), 400

        # 检查文件大小
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > MAX_FACE_SIZE:
            return jsonify({'error': '文件大小不能超过10MB'}), 400

        # 生成用户专属的人脸文件名
        filename = generate_user_face_filename(target_user_id, file.filename)
        file_path = os.path.join(FACE_UPLOAD_FOLDER, filename)
        print(f"用户 {current_user_id} 为用户 {target_user_id} 上传人脸，生成文件名: {filename}")

        # 保存文件
        file.save(file_path)

        # 调整图片大小（可选）
        if not resize_image(file_path):
            os.remove(file_path)  # 删除处理失败的文件
            return jsonify({'error': '图片处理失败'}), 500

        # 更新数据库中的人脸信息
        connection = get_db_connection()
        if not connection:
            os.remove(file_path)  # 删除已上传的文件
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取用户当前人脸信息（用于删除旧文件）
        query = "SELECT face_info FROM user WHERE id = %s"
        cursor.execute(query, (target_user_id,))
        user = cursor.fetchone()

        old_face_info = user['face_info'] if user else None

        # 更新人脸信息
        update_query = "UPDATE user SET face_info = %s WHERE id = %s"
        cursor.execute(update_query, (filename, target_user_id))

        # 删除旧人脸文件（如果存在且不同于新文件）
        if old_face_info and old_face_info != filename:
            old_file_path = os.path.join(FACE_UPLOAD_FOLDER, old_face_info)
            if os.path.exists(old_file_path):
                try:
                    os.remove(old_file_path)
                    print(f"成功删除旧人脸文件: {old_face_info}")
                except Exception as e:
                    print(f"删除旧人脸图片失败: {e}")
            else:
                print(f"旧人脸文件不存在: {old_file_path}")

        return jsonify({
            'message': '人脸图片上传成功',
            'face_info': filename
        }), 200

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取人脸图片API
@app.route('/api/user/face/<filename>', methods=['GET'])
def get_face_image(filename):
    try:
        # 安全检查文件名
        filename = secure_filename(filename)
        file_path = os.path.join(FACE_UPLOAD_FOLDER, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({'error': '人脸图片文件不存在'}), 404

        # 返回文件
        from flask import send_file
        return send_file(file_path)

    except Exception as e:
        return jsonify({'error': f'获取人脸图片失败: {str(e)}'}), 500

# 删除人脸图片API
@app.route('/api/user/face', methods=['DELETE'])
@token_required
def delete_face(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取用户当前人脸信息
        query = "SELECT face_info FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or not user['face_info']:
            return jsonify({'error': '用户没有设置人脸信息'}), 404

        # 删除人脸文件
        file_path = os.path.join(FACE_UPLOAD_FOLDER, user['face_info'])
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as e:
                print(f"删除人脸文件失败: {e}")

        # 更新数据库，清除人脸信息
        update_query = "UPDATE user SET face_info = NULL WHERE id = %s"
        cursor.execute(update_query, (current_user_id,))

        return jsonify({'message': '人脸信息删除成功'}), 200

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 批量上传人脸API（管理员功能）
@app.route('/api/admin/face/batch', methods=['POST'])
@token_required
def batch_upload_faces(current_user_id):
    try:
        # 检查当前用户是否为管理员
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证管理员权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以批量上传'}), 403

        data = request.get_json()
        if not data or 'faces' not in data:
            return jsonify({'error': '请提供人脸数据'}), 400

        results = []
        for face_data in data['faces']:
            user_id = face_data.get('user_id')
            face_filename = face_data.get('face_filename')

            if not user_id or not face_filename:
                results.append({
                    'user_id': user_id,
                    'success': False,
                    'error': '缺少必要参数'
                })
                continue

            try:
                # 更新用户人脸信息
                update_query = "UPDATE user SET face_info = %s WHERE id = %s"
                cursor.execute(update_query, (face_filename, user_id))

                results.append({
                    'user_id': user_id,
                    'success': True,
                    'message': '人脸信息更新成功'
                })
            except Exception as e:
                results.append({
                    'user_id': user_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'message': '批量上传完成',
            'results': results
        }), 200

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 管理员删除指定用户人脸API
@app.route('/api/admin/user/<int:user_id>/face', methods=['DELETE'])
@token_required
def admin_delete_user_face(current_user_id, user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证管理员权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        admin_user = cursor.fetchone()

        if not admin_user or admin_user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以删除用户人脸'}), 403

        # 获取目标用户的人脸信息
        query = "SELECT face_info FROM user WHERE id = %s"
        cursor.execute(query, (user_id,))
        target_user = cursor.fetchone()

        if not target_user:
            return jsonify({'error': '用户不存在'}), 404

        if not target_user['face_info']:
            return jsonify({'error': '该用户没有设置人脸信息'}), 404

        # 删除人脸文件
        file_path = os.path.join(FACE_UPLOAD_FOLDER, target_user['face_info'])
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"管理员删除用户{user_id}的人脸文件: {target_user['face_info']}")
            except Exception as e:
                print(f"删除人脸文件失败: {e}")

        # 更新数据库，清除人脸信息
        update_query = "UPDATE user SET face_info = NULL WHERE id = %s"
        cursor.execute(update_query, (user_id,))

        return jsonify({'message': '用户人脸信息删除成功'}), 200

    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 管理员获取指定用户信息API
@app.route('/api/admin/user/<int:user_id>', methods=['GET'])
@token_required
def admin_get_user(current_user_id, user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证管理员权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        admin_user = cursor.fetchone()

        if not admin_user or admin_user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以查看用户信息'}), 403

        # 获取指定用户信息
        query = """
            SELECT id, username, email, sex, birth_date, grade, department, number, phone, role,
                   face_info, avatar, created_at, updated_at, is_active,
                   CASE
                       WHEN birth_date IS NOT NULL THEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE())
                       ELSE NULL
                   END as age
            FROM user
            WHERE id = %s
        """
        cursor.execute(query, (user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': '用户不存在'}), 404

        # 格式化日期
        if user['created_at']:
            user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if user['updated_at']:
            user['updated_at'] = user['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        if user['birth_date']:
            user['birth_date'] = user['birth_date'].strftime('%Y-%m-%d')

        # 根据角色设置标签
        if user['role'] == 'admin':
            user['tags'] = ['管理员']
        elif user['role'] == 'teacher':
            user['tags'] = ['教师']
        else:
            user['tags'] = ['学生']

        return jsonify({'user': user}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 管理员更新用户信息API
@app.route('/api/admin/user/<int:user_id>', methods=['PUT'])
@token_required
def admin_update_user(current_user_id, user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供要更新的数据'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证管理员权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        admin_user = cursor.fetchone()

        if not admin_user or admin_user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以编辑用户信息'}), 403

        # 检查目标用户是否存在
        query = "SELECT id FROM user WHERE id = %s"
        cursor.execute(query, (user_id,))
        target_user = cursor.fetchone()

        if not target_user:
            return jsonify({'error': '目标用户不存在'}), 404

        # 构建更新字段和值
        update_fields = []
        update_values = []

        # 允许更新的字段
        allowed_fields = ['username', 'email', 'sex', 'birth_date', 'grade', 'department', 'number', 'phone', 'role']

        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = %s")
                update_values.append(data[field])

        if not update_fields:
            return jsonify({'error': '没有提供有效的更新字段'}), 400

        # 检查用户名、邮箱和学号/教职号的唯一性
        if 'username' in data:
            check_query = "SELECT id FROM user WHERE username = %s AND id != %s"
            cursor.execute(check_query, (data['username'], user_id))
            if cursor.fetchone():
                return jsonify({'error': '该用户名已被其他用户使用'}), 400

        if 'email' in data:
            check_query = "SELECT id FROM user WHERE email = %s AND id != %s"
            cursor.execute(check_query, (data['email'], user_id))
            if cursor.fetchone():
                return jsonify({'error': '该邮箱已被其他用户使用'}), 400

        if 'number' in data and data['number']:
            check_query = "SELECT id FROM user WHERE number = %s AND id != %s"
            cursor.execute(check_query, (data['number'], user_id))
            if cursor.fetchone():
                return jsonify({'error': '该学号/教职号已被其他用户使用'}), 400

        # 添加更新时间
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        update_values.append(user_id)

        # 构建并执行更新查询
        update_query = f"UPDATE user SET {', '.join(update_fields)} WHERE id = %s"
        cursor.execute(update_query, update_values)

        return jsonify({'message': '用户信息更新成功'}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 管理员创建用户API
@app.route('/api/admin/user', methods=['POST'])
@token_required
def admin_create_user(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供用户数据'}), 400

        # 验证必填字段
        required_fields = ['username', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} 是必填字段'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证管理员权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        admin_user = cursor.fetchone()

        if not admin_user or admin_user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以创建用户'}), 403

        # 检查用户名和邮箱是否已存在
        check_query = "SELECT id FROM user WHERE username = %s OR email = %s"
        cursor.execute(check_query, (data['username'], data['email']))
        if cursor.fetchone():
            return jsonify({'error': '用户名或邮箱已存在'}), 400

        # 检查学号/教职号是否已存在（如果提供了）
        if data.get('number'):
            check_query = "SELECT id FROM user WHERE number = %s"
            cursor.execute(check_query, (data['number'],))
            if cursor.fetchone():
                return jsonify({'error': '该学号/教职号已存在'}), 400

        # 加密密码
        password_hash = generate_password_hash(data['password'])

        # 插入新用户
        insert_query = """
            INSERT INTO user (username, email, password_hash, sex, birth_date, grade, department, number, phone, role)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        cursor.execute(insert_query, (
            data['username'],
            data['email'],
            password_hash,
            data.get('sex', ''),
            data.get('birth_date', None),
            data.get('grade', ''),
            data.get('department', ''),
            data.get('number', ''),
            data.get('phone', ''),
            data.get('role', 'student')
        ))

        # 获取新创建的用户ID
        new_user_id = cursor.lastrowid

        return jsonify({
            'message': '用户创建成功',
            'user_id': new_user_id
        }), 201

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 管理员删除用户API
@app.route('/api/admin/user/<int:user_id>', methods=['DELETE'])
@token_required
def admin_delete_user(current_user_id, user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证管理员权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        admin_user = cursor.fetchone()

        if not admin_user or admin_user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以删除用户'}), 403

        # 防止删除自己
        if current_user_id == user_id:
            return jsonify({'error': '不能删除自己的账户'}), 400

        # 获取要删除的用户信息
        query = "SELECT avatar, face_info FROM user WHERE id = %s"
        cursor.execute(query, (user_id,))
        user_to_delete = cursor.fetchone()

        if not user_to_delete:
            return jsonify({'error': '用户不存在'}), 404

        # 删除用户的头像文件
        if user_to_delete['avatar']:
            avatar_path = os.path.join(UPLOAD_FOLDER, user_to_delete['avatar'])
            if os.path.exists(avatar_path):
                try:
                    os.remove(avatar_path)
                    print(f"删除用户头像文件: {user_to_delete['avatar']}")
                except Exception as e:
                    print(f"删除头像文件失败: {e}")

        # 删除用户的人脸文件
        if user_to_delete['face_info']:
            face_path = os.path.join(FACE_UPLOAD_FOLDER, user_to_delete['face_info'])
            if os.path.exists(face_path):
                try:
                    os.remove(face_path)
                    print(f"删除用户人脸文件: {user_to_delete['face_info']}")
                except Exception as e:
                    print(f"删除人脸文件失败: {e}")

        # 删除用户记录
        delete_query = "DELETE FROM user WHERE id = %s"
        cursor.execute(delete_query, (user_id,))

        return jsonify({'message': '用户删除成功'}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 管理员批量删除用户API
@app.route('/api/admin/users/batch-delete', methods=['POST'])
@token_required
def admin_batch_delete_users(current_user_id):
    try:
        data = request.get_json()
        if not data or 'user_ids' not in data:
            return jsonify({'error': '请提供要删除的用户ID列表'}), 400

        user_ids = data['user_ids']
        if not isinstance(user_ids, list) or not user_ids:
            return jsonify({'error': '用户ID列表不能为空'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证管理员权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        admin_user = cursor.fetchone()

        if not admin_user or admin_user['role'] != 'admin':
            return jsonify({'error': '权限不足，仅管理员可以删除用户'}), 403

        # 防止删除自己
        if current_user_id in user_ids:
            return jsonify({'error': '不能删除自己的账户'}), 400

        # 获取要删除的用户信息
        placeholders = ','.join(['%s'] * len(user_ids))
        query = f"SELECT id, avatar, face_info FROM user WHERE id IN ({placeholders})"
        cursor.execute(query, user_ids)
        users_to_delete = cursor.fetchall()

        deleted_count = 0
        for user in users_to_delete:
            try:
                # 删除用户的头像文件
                if user['avatar']:
                    avatar_path = os.path.join(UPLOAD_FOLDER, user['avatar'])
                    if os.path.exists(avatar_path):
                        os.remove(avatar_path)

                # 删除用户的人脸文件
                if user['face_info']:
                    face_path = os.path.join(FACE_UPLOAD_FOLDER, user['face_info'])
                    if os.path.exists(face_path):
                        os.remove(face_path)

                deleted_count += 1
            except Exception as e:
                print(f"删除用户 {user['id']} 的文件时出错: {e}")

        # 批量删除用户记录
        delete_query = f"DELETE FROM user WHERE id IN ({placeholders})"
        cursor.execute(delete_query, user_ids)

        return jsonify({
            'message': f'成功删除 {deleted_count} 个用户',
            'deleted_count': deleted_count
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# ==================== 课程管理API ====================

# 获取课程列表API
@app.route('/api/courses', methods=['GET'])
@token_required
def get_courses(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取查询参数
        department = request.args.get('department')
        semester = request.args.get('semester')
        teacher_id = request.args.get('teacher_id')

        # 构建查询条件
        where_conditions = ['c.is_active = 1']
        params = []

        if department:
            where_conditions.append('c.department = %s')
            params.append(department)

        if semester:
            where_conditions.append('c.semester = %s')
            params.append(semester)

        if teacher_id:
            where_conditions.append('c.teacher_id = %s')
            params.append(teacher_id)

        where_clause = ' AND '.join(where_conditions)

        # 查询课程信息，包含教师信息
        query = f"""
            SELECT c.*, u.username as teacher_name, u.email as teacher_email
            FROM course c
            LEFT JOIN user u ON c.teacher_id = u.id
            WHERE {where_clause}
            ORDER BY c.day_of_week, c.start_time
        """

        cursor.execute(query, params)
        courses = cursor.fetchall()

        # 格式化时间字段
        for course in courses:
            if course['start_time']:
                course['start_time'] = str(course['start_time'])
            if course['end_time']:
                course['end_time'] = str(course['end_time'])
            if course['created_at']:
                course['created_at'] = course['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if course['updated_at']:
                course['updated_at'] = course['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'courses': courses,
            'total': len(courses)
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 创建课程API（管理员和教师权限）
@app.route('/api/courses', methods=['POST'])
@token_required
def create_course(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供课程数据'}), 400

        # 验证必填字段
        required_fields = ['course_name', 'start_time', 'end_time', 'day_of_week']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} 是必填字段'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证权限（管理员或教师）
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or user['role'] not in ['admin', 'teacher']:
            return jsonify({'error': '权限不足，仅管理员和教师可以创建课程'}), 403

        # 插入新课程
        insert_query = """
            INSERT INTO course (course_name, course_code, teacher_id, classroom, start_time, end_time,
                              day_of_week, weeks, department, semester, academic_year, credits,
                              course_type, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        cursor.execute(insert_query, (
            data['course_name'],
            data.get('course_code', ''),
            data.get('teacher_id', current_user_id if user['role'] == 'teacher' else None),
            data.get('classroom', ''),
            data['start_time'],
            data['end_time'],
            data['day_of_week'],
            data.get('weeks', ''),
            data.get('department', ''),
            data.get('semester', ''),
            data.get('academic_year', ''),
            data.get('credits', None),
            data.get('course_type', ''),
            data.get('description', '')
        ))

        course_id = cursor.lastrowid

        return jsonify({
            'message': '课程创建成功',
            'course_id': course_id
        }), 201

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 更新课程API
@app.route('/api/courses/<int:course_id>', methods=['PUT'])
@token_required
def update_course(current_user_id, course_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供要更新的数据'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': '用户不存在'}), 404

        # 检查课程是否存在以及权限
        query = "SELECT teacher_id FROM course WHERE id = %s"
        cursor.execute(query, (course_id,))
        course = cursor.fetchone()

        if not course:
            return jsonify({'error': '课程不存在'}), 404

        # 权限检查：管理员可以修改所有课程，教师只能修改自己的课程
        if user['role'] != 'admin' and course['teacher_id'] != current_user_id:
            return jsonify({'error': '权限不足，只能修改自己的课程'}), 403

        # 构建更新字段
        update_fields = []
        update_values = []

        allowed_fields = ['course_name', 'course_code', 'teacher_id', 'classroom', 'start_time',
                         'end_time', 'day_of_week', 'weeks', 'department', 'semester',
                         'academic_year', 'credits', 'course_type', 'description', 'is_active']

        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = %s")
                update_values.append(data[field])

        if not update_fields:
            return jsonify({'error': '没有提供有效的更新字段'}), 400

        # 添加更新时间
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        update_values.append(course_id)

        # 执行更新
        update_query = f"UPDATE course SET {', '.join(update_fields)} WHERE id = %s"
        cursor.execute(update_query, update_values)

        return jsonify({'message': '课程更新成功'}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 删除课程API
@app.route('/api/courses/<int:course_id>', methods=['DELETE'])
@token_required
def delete_course(current_user_id, course_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or user['role'] not in ['admin', 'teacher']:
            return jsonify({'error': '权限不足，仅管理员和教师可以删除课程'}), 403

        # 检查课程是否存在以及权限
        query = "SELECT teacher_id FROM course WHERE id = %s"
        cursor.execute(query, (course_id,))
        course = cursor.fetchone()

        if not course:
            return jsonify({'error': '课程不存在'}), 404

        # 权限检查：管理员可以删除所有课程，教师只能删除自己的课程
        if user['role'] != 'admin' and course['teacher_id'] != current_user_id:
            return jsonify({'error': '权限不足，只能删除自己的课程'}), 403

        # 删除课程
        delete_query = "DELETE FROM course WHERE id = %s"
        cursor.execute(delete_query, (course_id,))

        return jsonify({'message': '课程删除成功'}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取单个课程详情API
@app.route('/api/courses/<int:course_id>', methods=['GET'])
@token_required
def get_course_detail(current_user_id, course_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 查询课程详情
        query = """
            SELECT c.*, u.username as teacher_name, u.email as teacher_email
            FROM course c
            LEFT JOIN user u ON c.teacher_id = u.id
            WHERE c.id = %s
        """
        cursor.execute(query, (course_id,))
        course = cursor.fetchone()

        if not course:
            return jsonify({'error': '课程不存在'}), 404

        # 格式化时间字段
        if course['start_time']:
            course['start_time'] = str(course['start_time'])
        if course['end_time']:
            course['end_time'] = str(course['end_time'])
        if course['created_at']:
            course['created_at'] = course['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if course['updated_at']:
            course['updated_at'] = course['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({'course': course}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取学期配置API
@app.route('/api/semester-config', methods=['GET'])
@token_required
def get_semester_config(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取查询参数
        semester = request.args.get('semester')

        if semester:
            # 查询特定学期配置
            query = "SELECT * FROM semester_config WHERE semester = %s AND is_active = 1"
            cursor.execute(query, (semester,))
            config = cursor.fetchone()

            if not config:
                return jsonify({'error': '学期配置不存在'}), 404

            # 格式化日期字段
            if config['start_date']:
                config['start_date'] = config['start_date'].strftime('%Y-%m-%d')
            if config['end_date']:
                config['end_date'] = config['end_date'].strftime('%Y-%m-%d')
            if config['created_at']:
                config['created_at'] = config['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if config['updated_at']:
                config['updated_at'] = config['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify({'config': config}), 200
        else:
            # 查询所有学期配置
            query = "SELECT * FROM semester_config WHERE is_active = 1 ORDER BY academic_year DESC, semester DESC"
            cursor.execute(query)
            configs = cursor.fetchall()

            # 格式化日期字段
            for config in configs:
                if config['start_date']:
                    config['start_date'] = config['start_date'].strftime('%Y-%m-%d')
                if config['end_date']:
                    config['end_date'] = config['end_date'].strftime('%Y-%m-%d')
                if config['created_at']:
                    config['created_at'] = config['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if config['updated_at']:
                    config['updated_at'] = config['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify({'configs': configs}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# ==================== 签到功能API ====================

# 学生签到API
@app.route('/api/attendance/check-in', methods=['POST'])
@token_required
def check_in(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供签到数据'}), 400

        student_number = data.get('student_number')
        course_id = data.get('course_id')

        if not student_number or not course_id:
            return jsonify({'error': '学号和课程ID是必填项'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 根据学号查找学生
        query = "SELECT id, username, face_info FROM user WHERE number = %s AND role = 'student'"
        cursor.execute(query, (student_number,))
        student = cursor.fetchone()

        if not student:
            return jsonify({'error': '学号不存在或不是学生账户'}), 404

        # 检查是否有人脸信息
        if not student['face_info']:
            return jsonify({'error': '该学生未录入人脸信息，无法进行人脸识别签到'}), 400

        # 检查课程是否存在
        query = "SELECT * FROM course WHERE id = %s AND is_active = 1"
        cursor.execute(query, (course_id,))
        course = cursor.fetchone()

        if not course:
            return jsonify({'error': '课程不存在或已停用'}), 404

        # 获取当前日期
        current_date = datetime.datetime.now().date()
        current_time = datetime.datetime.now()

        # 检查是否已经签到过
        query = """
            SELECT id, status, check_in_time FROM attendance_record
            WHERE course_id = %s AND student_id = %s AND attendance_date = %s
        """
        cursor.execute(query, (course_id, student['id'], current_date))
        existing_record = cursor.fetchone()

        if existing_record and existing_record['check_in_time']:
            return jsonify({
                'error': '今日已签到',
                'check_in_time': existing_record['check_in_time'].strftime('%Y-%m-%d %H:%M:%S')
            }), 400

        # 判断签到状态（这里简化处理，实际应该根据课程时间判断）
        course_start_time = datetime.datetime.combine(current_date, course['start_time'])
        if current_time <= course_start_time:
            status = 'present'
        elif current_time <= course_start_time + datetime.timedelta(minutes=15):
            status = 'late'
        else:
            status = 'truant'

        # 插入或更新签到记录
        if existing_record:
            update_query = """
                UPDATE attendance_record
                SET check_in_time = %s, status = %s, face_recognition_result = %s,
                    ip_address = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """
            cursor.execute(update_query, (
                current_time, status, '人脸识别成功',
                request.remote_addr, existing_record['id']
            ))
        else:
            insert_query = """
                INSERT INTO attendance_record (course_id, student_id, attendance_date, check_in_time,
                                             status, face_recognition_result, ip_address)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (
                course_id, student['id'], current_date, current_time,
                status, '人脸识别成功', request.remote_addr
            ))

        return jsonify({
            'message': '签到成功',
            'student_name': student['username'],
            'status': status,
            'check_in_time': current_time.strftime('%Y-%m-%d %H:%M:%S')
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 生成签退码API（教师功能）
@app.route('/api/attendance/generate-checkout-code', methods=['POST'])
@token_required
def generate_checkout_code(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供课程信息'}), 400

        course_id = data.get('course_id')
        duration_minutes = data.get('duration_minutes', 10)  # 默认10分钟有效期

        if not course_id:
            return jsonify({'error': '课程ID是必填项'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证教师权限和课程归属
        query = """
            SELECT c.*, u.role FROM course c
            JOIN user u ON c.teacher_id = u.id
            WHERE c.id = %s AND c.teacher_id = %s
        """
        cursor.execute(query, (course_id, current_user_id))
        course = cursor.fetchone()

        if not course:
            return jsonify({'error': '课程不存在或您不是该课程的授课教师'}), 403

        # 生成随机签退码
        session_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

        # 设置有效期
        start_time = datetime.datetime.now()
        end_time = start_time + datetime.timedelta(minutes=duration_minutes)

        # 插入签退会话
        insert_query = """
            INSERT INTO check_out_session (course_id, teacher_id, session_code, start_time, end_time, description)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        cursor.execute(insert_query, (
            course_id, current_user_id, session_code, start_time, end_time,
            data.get('description', '随机签退')
        ))

        return jsonify({
            'message': '签退码生成成功',
            'session_code': session_code,
            'valid_until': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration_minutes': duration_minutes
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 学生签退API
@app.route('/api/attendance/check-out', methods=['POST'])
@token_required
def check_out(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供签退数据'}), 400

        student_number = data.get('student_number')
        session_code = data.get('session_code')

        if not student_number or not session_code:
            return jsonify({'error': '学号和签退码是必填项'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证签退码
        query = """
            SELECT * FROM check_out_session
            WHERE session_code = %s AND is_active = 1 AND NOW() BETWEEN start_time AND end_time
        """
        cursor.execute(query, (session_code,))
        session = cursor.fetchone()

        if not session:
            return jsonify({'error': '签退码无效或已过期'}), 400

        # 根据学号查找学生
        query = "SELECT id, username, face_info FROM user WHERE number = %s AND role = 'student'"
        cursor.execute(query, (student_number,))
        student = cursor.fetchone()

        if not student:
            return jsonify({'error': '学号不存在或不是学生账户'}), 404

        # 检查是否有人脸信息
        if not student['face_info']:
            return jsonify({'error': '该学生未录入人脸信息，无法进行人脸识别签退'}), 400

        # 获取当前日期和时间
        current_date = datetime.datetime.now().date()
        current_time = datetime.datetime.now()

        # 查找今日的签到记录
        query = """
            SELECT * FROM attendance_record
            WHERE course_id = %s AND student_id = %s AND attendance_date = %s
        """
        cursor.execute(query, (session['course_id'], student['id'], current_date))
        attendance_record = cursor.fetchone()

        if not attendance_record:
            return jsonify({'error': '未找到今日签到记录，请先签到'}), 400

        if attendance_record['check_out_time']:
            return jsonify({
                'error': '今日已签退',
                'check_out_time': attendance_record['check_out_time'].strftime('%Y-%m-%d %H:%M:%S')
            }), 400

        # 更新签退时间
        update_query = """
            UPDATE attendance_record
            SET check_out_time = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """
        cursor.execute(update_query, (current_time, attendance_record['id']))

        return jsonify({
            'message': '签退成功',
            'student_name': student['username'],
            'check_out_time': current_time.strftime('%Y-%m-%d %H:%M:%S')
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 调整学生签到状态API（教师功能）
@app.route('/api/attendance/adjust-status', methods=['PUT'])
@token_required
def adjust_attendance_status(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供调整数据'}), 400

        attendance_id = data.get('attendance_id')
        new_status = data.get('status')
        reason = data.get('reason', '')

        if not attendance_id or not new_status:
            return jsonify({'error': '签到记录ID和新状态是必填项'}), 400

        # 验证状态值
        valid_statuses = ['present', 'late', 'absent', 'truant', 'early_leave', 'sick_leave', 'personal_leave', 'official_leave']
        if new_status not in valid_statuses:
            return jsonify({'error': '无效的状态值'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证教师权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or user['role'] not in ['admin', 'teacher']:
            return jsonify({'error': '权限不足，仅管理员和教师可以调整签到状态'}), 403

        # 获取签到记录和课程信息
        query = """
            SELECT ar.*, c.teacher_id FROM attendance_record ar
            JOIN course c ON ar.course_id = c.id
            WHERE ar.id = %s
        """
        cursor.execute(query, (attendance_id,))
        record = cursor.fetchone()

        if not record:
            return jsonify({'error': '签到记录不存在'}), 404

        # 权限检查：管理员可以调整所有记录，教师只能调整自己课程的记录
        if user['role'] != 'admin' and record['teacher_id'] != current_user_id:
            return jsonify({'error': '权限不足，只能调整自己课程的签到记录'}), 403

        # 保存原始状态（如果还没有保存过）
        original_status = record['original_status'] or record['status']

        # 更新签到状态
        update_query = """
            UPDATE attendance_record
            SET status = %s, original_status = %s, adjusted_by = %s,
                adjusted_at = CURRENT_TIMESTAMP, adjustment_reason = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """
        cursor.execute(update_query, (new_status, original_status, current_user_id, reason, attendance_id))

        return jsonify({'message': '签到状态调整成功'}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# ==================== 请假系统API ====================

# 学生申请请假API
@app.route('/api/leave/apply', methods=['POST'])
@token_required
def apply_leave(current_user_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供请假申请数据'}), 400

        # 验证必填字段
        required_fields = ['leave_type', 'start_date', 'end_date', 'reason']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} 是必填字段'}), 400

        # 验证请假类型
        valid_leave_types = ['sick_leave', 'personal_leave', 'official_leave']
        if data['leave_type'] not in valid_leave_types:
            return jsonify({'error': '无效的请假类型'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证用户是学生
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or user['role'] != 'student':
            return jsonify({'error': '只有学生可以申请请假'}), 403

        # 插入请假申请
        insert_query = """
            INSERT INTO leave_application (student_id, leave_type, start_date, end_date,
                                         start_time, end_time, reason)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        cursor.execute(insert_query, (
            current_user_id,
            data['leave_type'],
            data['start_date'],
            data['end_date'],
            data.get('start_time'),
            data.get('end_time'),
            data['reason']
        ))

        application_id = cursor.lastrowid

        return jsonify({
            'message': '请假申请提交成功',
            'application_id': application_id
        }), 201

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取请假申请列表API
@app.route('/api/leave/applications', methods=['GET'])
@token_required
def get_leave_applications(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取用户角色
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': '用户不存在'}), 404

        # 根据角色构建查询
        if user['role'] == 'student':
            # 学生只能查看自己的申请
            query = """
                SELECT la.*, u.username as student_name, u.number as student_number,
                       approver.username as approver_name
                FROM leave_application la
                JOIN user u ON la.student_id = u.id
                LEFT JOIN user approver ON la.approved_by = approver.id
                WHERE la.student_id = %s
                ORDER BY la.created_at DESC
            """
            cursor.execute(query, (current_user_id,))
        else:
            # 教师和管理员可以查看所有申请
            status_filter = request.args.get('status')
            where_conditions = []
            params = []

            if status_filter:
                where_conditions.append('la.status = %s')
                params.append(status_filter)

            where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'

            query = f"""
                SELECT la.*, u.username as student_name, u.number as student_number,
                       approver.username as approver_name
                FROM leave_application la
                JOIN user u ON la.student_id = u.id
                LEFT JOIN user approver ON la.approved_by = approver.id
                WHERE {where_clause}
                ORDER BY la.created_at DESC
            """
            cursor.execute(query, params)

        applications = cursor.fetchall()

        # 格式化日期时间
        for app in applications:
            if app['start_date']:
                app['start_date'] = app['start_date'].strftime('%Y-%m-%d')
            if app['end_date']:
                app['end_date'] = app['end_date'].strftime('%Y-%m-%d')
            if app['start_time']:
                app['start_time'] = str(app['start_time'])
            if app['end_time']:
                app['end_time'] = str(app['end_time'])
            if app['created_at']:
                app['created_at'] = app['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if app['approved_at']:
                app['approved_at'] = app['approved_at'].strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'applications': applications,
            'total': len(applications)
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 审批请假申请API（教师和管理员功能）
@app.route('/api/leave/applications/<int:application_id>/approve', methods=['PUT'])
@token_required
def approve_leave_application(current_user_id, application_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请提供审批数据'}), 400

        action = data.get('action')  # 'approve' 或 'reject'
        comment = data.get('comment', '')

        if action not in ['approve', 'reject']:
            return jsonify({'error': '无效的审批操作'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 验证权限
        query = "SELECT role FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user or user['role'] not in ['admin', 'teacher']:
            return jsonify({'error': '权限不足，仅管理员和教师可以审批请假申请'}), 403

        # 检查申请是否存在
        query = "SELECT * FROM leave_application WHERE id = %s"
        cursor.execute(query, (application_id,))
        application = cursor.fetchone()

        if not application:
            return jsonify({'error': '请假申请不存在'}), 404

        if application['status'] != 'pending':
            return jsonify({'error': '该申请已经被处理过了'}), 400

        # 更新申请状态
        new_status = 'approved' if action == 'approve' else 'rejected'
        update_query = """
            UPDATE leave_application
            SET status = %s, approved_by = %s, approved_at = CURRENT_TIMESTAMP,
                approval_comment = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """
        cursor.execute(update_query, (new_status, current_user_id, comment, application_id))

        return jsonify({
            'message': f'请假申请已{("批准" if action == "approve" else "拒绝")}'
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# ==================== 签到统计API ====================

# 获取签到记录API
@app.route('/api/attendance/records', methods=['GET'])
@token_required
def get_attendance_records(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取查询参数
        course_id = request.args.get('course_id')
        department = request.args.get('department')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')
        student_id = request.args.get('student_id')

        # 构建查询条件
        where_conditions = ['1=1']
        params = []

        if course_id:
            where_conditions.append('ar.course_id = %s')
            params.append(course_id)

        if department:
            where_conditions.append('u.department = %s')
            params.append(department)

        if start_date:
            where_conditions.append('ar.attendance_date >= %s')
            params.append(start_date)

        if end_date:
            where_conditions.append('ar.attendance_date <= %s')
            params.append(end_date)

        if status:
            where_conditions.append('ar.status = %s')
            params.append(status)

        if student_id:
            where_conditions.append('ar.student_id = %s')
            params.append(student_id)

        where_clause = ' AND '.join(where_conditions)

        # 查询签到记录
        query = f"""
            SELECT ar.*, u.username as student_name, u.number as student_number, u.department,
                   c.course_name, c.classroom, c.start_time as course_start_time,
                   adjuster.username as adjuster_name
            FROM attendance_record ar
            JOIN user u ON ar.student_id = u.id
            JOIN course c ON ar.course_id = c.id
            LEFT JOIN user adjuster ON ar.adjusted_by = adjuster.id
            WHERE {where_clause}
            ORDER BY ar.attendance_date DESC, c.start_time DESC
        """

        cursor.execute(query, params)
        records = cursor.fetchall()

        # 格式化时间字段
        for record in records:
            if record['attendance_date']:
                record['attendance_date'] = record['attendance_date'].strftime('%Y-%m-%d')
            if record['check_in_time']:
                record['check_in_time'] = record['check_in_time'].strftime('%Y-%m-%d %H:%M:%S')
            if record['check_out_time']:
                record['check_out_time'] = record['check_out_time'].strftime('%Y-%m-%d %H:%M:%S')
            if record['adjusted_at']:
                record['adjusted_at'] = record['adjusted_at'].strftime('%Y-%m-%d %H:%M:%S')
            if record['course_start_time']:
                record['course_start_time'] = str(record['course_start_time'])

        return jsonify({
            'records': records,
            'total': len(records)
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取签到统计API
@app.route('/api/attendance/statistics', methods=['GET'])
@token_required
def get_attendance_statistics(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取查询参数
        course_id = request.args.get('course_id')
        department = request.args.get('department')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 构建查询条件
        where_conditions = ['1=1']
        params = []

        if course_id:
            where_conditions.append('ar.course_id = %s')
            params.append(course_id)

        if department:
            where_conditions.append('u.department = %s')
            params.append(department)

        if start_date:
            where_conditions.append('ar.attendance_date >= %s')
            params.append(start_date)

        if end_date:
            where_conditions.append('ar.attendance_date <= %s')
            params.append(end_date)

        where_clause = ' AND '.join(where_conditions)

        # 统计各种状态的数量
        stats_query = f"""
            SELECT
                ar.status,
                COUNT(*) as count,
                COUNT(CASE WHEN ar.adjusted_by IS NOT NULL THEN 1 END) as adjusted_count
            FROM attendance_record ar
            JOIN user u ON ar.student_id = u.id
            WHERE {where_clause}
            GROUP BY ar.status
        """

        cursor.execute(stats_query, params)
        status_stats = cursor.fetchall()

        # 按学生统计
        student_stats_query = f"""
            SELECT
                u.id as student_id,
                u.username as student_name,
                u.number as student_number,
                u.department,
                COUNT(*) as total_records,
                COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN ar.status = 'truant' THEN 1 END) as truant_count,
                COUNT(CASE WHEN ar.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN ar.status = 'sick_leave' THEN 1 END) as sick_leave_count,
                COUNT(CASE WHEN ar.status = 'personal_leave' THEN 1 END) as personal_leave_count,
                COUNT(CASE WHEN ar.status = 'official_leave' THEN 1 END) as official_leave_count,
                COUNT(CASE WHEN ar.adjusted_by IS NOT NULL THEN 1 END) as adjusted_count
            FROM attendance_record ar
            JOIN user u ON ar.student_id = u.id
            WHERE {where_clause}
            GROUP BY u.id, u.username, u.number, u.department
            ORDER BY u.department, u.number
        """

        cursor.execute(student_stats_query, params)
        student_stats = cursor.fetchall()

        # 按课程统计
        course_stats_query = f"""
            SELECT
                c.id as course_id,
                c.course_name,
                c.department,
                COUNT(*) as total_records,
                COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN ar.status = 'truant' THEN 1 END) as truant_count
            FROM attendance_record ar
            JOIN course c ON ar.course_id = c.id
            JOIN user u ON ar.student_id = u.id
            WHERE {where_clause}
            GROUP BY c.id, c.course_name, c.department
            ORDER BY c.department, c.course_name
        """

        cursor.execute(course_stats_query, params)
        course_stats = cursor.fetchall()

        return jsonify({
            'status_statistics': status_stats,
            'student_statistics': student_stats,
            'course_statistics': course_stats
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取学期列表API
@app.route('/api/semesters', methods=['GET'])
@token_required
def get_semesters(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        query = """
            SELECT semester, academic_year, start_date, end_date, total_weeks, is_active
            FROM semester_config
            WHERE is_active = 1
            ORDER BY academic_year DESC, semester DESC
        """
        cursor.execute(query)
        semesters = cursor.fetchall()

        return jsonify({
            'semesters': semesters,
            'total': len(semesters)
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取部门/班级列表API
@app.route('/api/departments', methods=['GET'])
@token_required
def get_departments(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor()

        query = """
            SELECT DISTINCT department
            FROM course
            WHERE department IS NOT NULL AND department != '' AND is_active = 1
            ORDER BY department
        """
        cursor.execute(query)
        departments = [row[0] for row in cursor.fetchall()]

        return jsonify({
            'departments': departments,
            'total': len(departments)
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取教师列表API（扩展现有的用户API）
@app.route('/api/users', methods=['GET'])
@token_required
def get_users_extended(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 获取查询参数
        role = request.args.get('role')

        # 构建查询条件
        where_conditions = ['is_active = 1']
        params = []

        if role:
            where_conditions.append('role = %s')
            params.append(role)

        query = f"""
            SELECT id, username, email, role, number, real_name, department, phone,
                   birth_date, gender, avatar, created_at
            FROM user
            WHERE {' AND '.join(where_conditions)}
            ORDER BY role, username
        """

        cursor.execute(query, params)
        users = cursor.fetchall()

        return jsonify({
            'users': users,
            'total': len(users)
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == '__main__':
    # 生产环境中应该设置 debug=False
    app.run(debug=os.environ.get('FLASK_ENV') == 'development',
            host='0.0.0.0',
            port=int(os.environ.get('PORT', 5000)))