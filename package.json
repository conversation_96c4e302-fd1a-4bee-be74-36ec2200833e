{"name": "security", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "sass-embedded": "^1.89.2", "vite": "^7.0.0"}}