<template>
  <div class="user-profile">
    <div class="profile-container" v-if="!loading">
      <div class="profile-header">
        <h2>{{ isViewingOtherUser ? '用户信息' : '个人设置' }}</h2>
        <p>{{ isViewingOtherUser ? `查看 ${user?.username} 的详细信息` : '管理您的个人信息和头像' }}</p>
      </div>

      <div class="profile-content">
        <!-- 左侧头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <AvatarUpload v-if="!isViewingOtherUser" :user="user" @avatar-updated="handleAvatarUpdate" />
            <div v-else class="user-avatar-display">
              <img
                v-if="user?.avatar"
                :src="authAPI.getAvatarUrl(user.avatar)"
                :alt="user.username + '的头像'"
                class="display-avatar"
              />
              <div v-else class="no-avatar-display">
                <user-outlined />
              </div>
              <p class="avatar-label">{{ user?.username }}</p>
            </div>
            <div class="profile-actions">
              <button @click="goBack" class="btn btn-secondary">
                {{ isViewingOtherUser ? '返回用户列表' : '返回首页' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 右侧基本信息区域 -->
        <div class="info-section">
          <div class="section-header">
            <div class="left-part">
              <div class="section-icon">ℹ️</div>
              <h3>基本信息</h3>
            </div>
            <button v-if="!isViewingOtherUser" @click="toggleEditMode" class="edit-btn">
              {{ isEditing ? '取消编辑' : '编辑信息' }}
            </button>
          </div>

          <!-- 编辑模式 -->
          <div v-if="isEditing" class="edit-form">
            <div class="form-grid">
              <div class="form-item">
                <label>邮箱</label>
                <input
                  v-model="editForm.email"
                  type="email"
                  class="form-input"
                  placeholder="请输入邮箱"
                />
              </div>
              <div class="form-item">
                <label>性别</label>
                <select v-model="editForm.sex" class="form-input">
                  <option value="">请选择</option>
                  <option value="男">男</option>
                  <option value="女">女</option>
                </select>
              </div>
              <div class="form-item">
                <label>出生日期</label>
                <input
                  v-model="editForm.birth_date"
                  type="date"
                  class="form-input"
                />
              </div>
              <div class="form-item">
                <label>年级</label>
                <input
                  v-model="editForm.grade"
                  type="text"
                  class="form-input"
                  placeholder="请输入年级"
                />
              </div>
              <div class="form-item">
                <label>班级/部门</label>
                <input
                  v-model="editForm.department"
                  type="text"
                  class="form-input"
                  placeholder="请输入班级或部门"
                />
              </div>
              <div class="form-item">
                <label>学号/教职号</label>
                <input
                  v-model="editForm.number"
                  type="text"
                  class="form-input"
                  placeholder="请输入学号或教职号"
                  :disabled="true"
                  title="学号/教职号不可修改"
                />
              </div>
              <div class="form-item">
                <label>联系方式</label>
                <input
                  v-model="editForm.phone"
                  type="tel"
                  class="form-input"
                  placeholder="请输入联系方式"
                />
              </div>
            </div>
            <div class="form-actions">
              <button @click="saveUserInfo" class="save-btn" :disabled="saving">
                {{ saving ? '保存中...' : '保存' }}
              </button>
              <button @click="cancelEdit" class="cancel-btn">取消</button>
            </div>
          </div>

          <!-- 显示模式 - 三列布局 -->
          <div v-else class="info-grid three-columns">
            <div class="info-item">
              <div class="info-icon">👤</div>
              <div class="info-content">
                <label>用户名</label>
                <div class="info-value">{{ user?.username || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">📧</div>
              <div class="info-content">
                <label>邮箱</label>
                <div class="info-value">{{ user?.email || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">⚥</div>
              <div class="info-content">
                <label>性别</label>
                <div class="info-value">{{ user?.sex || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🔢</div>
              <div class="info-content">
                <label>年龄</label>
                <div class="info-value">{{ user?.age ? user.age + '岁' : '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">📅</div>
              <div class="info-content">
                <label>出生日期</label>
                <div class="info-value">{{ user?.birth_date || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🎓</div>
              <div class="info-content">
                <label>年级</label>
                <div class="info-value">{{ user?.grade || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🏢</div>
              <div class="info-content">
                <label>班级/部门</label>
                <div class="info-value">{{ user?.department || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🆔</div>
              <div class="info-content">
                <label>学号/教职号</label>
                <div class="info-value">{{ user?.number || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">📱</div>
              <div class="info-content">
                <label>联系方式</label>
                <div class="info-value">{{ user?.phone || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🏷️</div>
              <div class="info-content">
                <label>角色权限</label>
                <div class="info-value">
                  <span class="role-tag" :class="getRoleClass(user?.role)">
                    {{ getRoleText(user?.role) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">📅</div>
              <div class="info-content">
                <label>注册时间</label>
                <div class="info-value">{{ formatDate(user?.created_at) || '未知' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">👤</div>
              <div class="info-content">
                <label>人脸信息</label>
                <div class="info-value face-info">
                  <div v-if="user?.face_info && getFaceUrl(user.face_info)" class="face-preview">
                    <img
                      :src="getFaceUrl(user.face_info)"
                      :alt="user.username + '的人脸'"
                      class="face-image"
                      @error="handleFaceError"
                      @load="handleFaceLoad"
                    />
                    <span class="face-status success">已设置</span>
                  </div>
                  <div v-else-if="user?.face_info && !getFaceUrl(user.face_info)" class="face-error-info">
                    <span class="face-status error">文件丢失</span>
                    <small>人脸文件不存在或已损坏</small>
                  </div>
                  <div v-else class="no-face">
                    <span>未设置人脸</span>
                  </div>
                  <button @click="manageFace" class="manage-face-btn">
                    {{ user?.face_info ? '更新人脸' : '设置人脸' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 人脸管理模态框 -->
    <a-modal
      v-model:open="faceModalVisible"
      title="人脸信息管理"
      width="700px"
      :footer="null"
      @cancel="closeFaceModal"
    >
      <FaceCapture
        v-if="user"
        user-id="current"
        :current-face-info="user.face_info"
        @face-updated="handleFaceUpdated"
        @face-deleted="handleFaceDeleted"
      />
    </a-modal>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Modal as AModal, message } from 'ant-design-vue';
import { UserOutlined } from '@ant-design/icons-vue';
import { authAPI, authUtils } from '../utils/api';
import AvatarUpload from './AvatarUpload.vue';
import FaceCapture from './FaceCapture.vue';

const router = useRouter();
const route = useRoute();
const user = ref(null);
const loading = ref(true);
const faceModalVisible = ref(false);
const isEditing = ref(false);
const saving = ref(false);
const isViewingOtherUser = ref(false);
const editForm = ref({
  email: '',
  sex: '',
  birth_date: '',
  grade: '',
  department: '',
  number: '',
  phone: ''
});

// 获取用户信息
const fetchUserProfile = async () => {
  loading.value = true;
  try {
    const userId = route.params.id;
    if (userId) {
      // 查看其他用户的信息（管理员功能）
      isViewingOtherUser.value = true;
      const response = await authAPI.adminGetUser(userId);
      user.value = response.data.user;
    } else {
      // 查看自己的信息
      isViewingOtherUser.value = false;
      const response = await authAPI.getUserProfile();
      user.value = response.data.user;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    if (!route.params.id) {
      // 如果获取失败，使用本地存储的用户信息
      user.value = authUtils.getCurrentUser();
    } else {
      // 查看其他用户失败，返回首页
      router.push('/home');
    }
  } finally {
    loading.value = false;
  }
};

// 处理头像更新
const handleAvatarUpdate = (newAvatar) => {
  if (user.value) {
    user.value.avatar = newAvatar;
  }

  // 触发全局状态更新
  window.dispatchEvent(new Event('authStateChanged'));
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return '日期格式错误';
  }
};

// 获取人脸图片URL
const getFaceUrl = (filename) => {
  if (!filename) {
    console.log('人脸文件名为空');
    return null;
  }
  const url = authAPI.getFaceUrl(filename);
  console.log('人脸图片URL:', url);
  return url;
};

// 检查人脸信息状态
const checkFaceStatus = (faceInfo) => {
  if (!faceInfo) {
    return { hasface: false, status: '未设置', message: '未设置人脸' };
  }

  // 检查文件是否存在（通过尝试加载图片）
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve({ hasface: true, status: '已设置', message: '人脸已设置' });
    };
    img.onerror = () => {
      console.error('人脸图片文件不存在或损坏:', faceInfo);
      resolve({ hasface: false, status: '文件丢失', message: '人脸文件丢失' });
    };
    img.src = getFaceUrl(faceInfo);
  });
};

// 处理人脸图片加载成功
const handleFaceLoad = (event) => {
  console.log('人脸图片加载成功:', event.target.src);
};

// 处理人脸图片加载错误
const handleFaceError = (event) => {
  console.error('人脸图片加载失败:', event.target.src);
  event.target.style.display = 'none';
  // 显示错误提示
  const parent = event.target.parentElement;
  if (parent) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'face-error';
    errorDiv.innerHTML = '<span>图片加载失败</span>';
    parent.appendChild(errorDiv);
  }
};

// 人脸管理
const manageFace = () => {
  faceModalVisible.value = true;
};

// 关闭人脸管理模态框
const closeFaceModal = () => {
  faceModalVisible.value = false;
};

// 处理人脸更新
const handleFaceUpdated = (faceInfo) => {
  if (user.value) {
    user.value.face_info = faceInfo;
    console.log('用户人脸信息已更新:', faceInfo);
  }
  message.success('人脸信息更新成功');
};

// 处理人脸删除
const handleFaceDeleted = () => {
  if (user.value) {
    user.value.face_info = null;
    console.log('用户人脸信息已删除');
  }
  message.success('人脸信息删除成功');
};

// 切换编辑模式
const toggleEditMode = () => {
  if (isEditing.value) {
    cancelEdit();
  } else {
    // 进入编辑模式，初始化表单数据
    editForm.value = {
      email: user.value?.email || '',
      sex: user.value?.sex || '',
      birth_date: user.value?.birth_date || '',
      grade: user.value?.grade || '',
      department: user.value?.department || '',
      number: user.value?.number || '',
      phone: user.value?.phone || ''
    };
    isEditing.value = true;
  }
};

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false;
  editForm.value = {
    email: '',
    sex: '',
    birth_date: '',
    grade: '',
    department: '',
    number: '',
    phone: ''
  };
};

// 保存用户信息
const saveUserInfo = async () => {
  try {
    saving.value = true;

    // 基本验证
    if (!editForm.value.email) {
      message.error('邮箱不能为空');
      return;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(editForm.value.email)) {
      message.error('请输入有效的邮箱地址');
      return;
    }

    // 手机号格式验证（如果填写了）
    if (editForm.value.phone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(editForm.value.phone)) {
        message.error('请输入有效的手机号码');
        return;
      }
    }

    const response = await authAPI.updateUserProfile(editForm.value);

    // 更新本地用户数据
    user.value = response.data.user;

    message.success('用户信息更新成功');
    isEditing.value = false;

    // 触发全局状态更新
    window.dispatchEvent(new Event('authStateChanged'));

  } catch (error) {
    console.error('更新用户信息失败:', error);
    message.error(error.response?.data?.error || '更新失败');
  } finally {
    saving.value = false;
  }
};

// 获取角色显示文本
const getRoleText = (role) => {
  const roleMap = {
    'admin': '管理员',
    'teacher': '教师',
    'student': '学生'
  };
  return roleMap[role] || '未知';
};

// 获取角色样式类
const getRoleClass = (role) => {
  const classMap = {
    'admin': 'role-admin',
    'teacher': 'role-teacher',
    'student': 'role-student'
  };
  return classMap[role] || 'role-default';
};

// 返回首页或用户列表
const goBack = () => {
  if (isViewingOtherUser.value) {
    router.push('/info');
  } else {
    router.push('/home');
  }
};

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserProfile();
});
</script>

<style scoped>
.user-profile {
  min-height: 88vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.user-profile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.2) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}

.user-profile {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.profile-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 50px;
  max-width: none;
  width: 80%;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  animation: slideUp 0.6s ease-out;
  font-size: 20px;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.profile-header::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.profile-header h2 {
  color: #2d3748;
  font-size: 3rem;
  margin-bottom: 12px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-header p {
  color: #718096;
  font-size: 1.4rem;
  font-weight: 400;
}

.profile-content {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 40px;
  align-items: start;
}

.avatar-section {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  border-radius: 16px;
  height: 66.5vh !important;
  padding: 25px 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  height: fit-content;
}

.avatar-section:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.avatar-container::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -20px;
  width: 1px;
  height: 50%;
  background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.3), transparent);
  transform: translateY(-50%);
}

/* 用户头像显示样式 */
.user-avatar-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.display-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.no-avatar-display {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 48px;
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.avatar-label {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin: 0;
}

.info-section {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  border-radius: 20px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  height: fit-content;
}

.info-section:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}

.section-header .left-part {
  display: flex;
  align-items: center;
  gap: 15px;
}

.section-header .section-icon {
  font-size: 1.8rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-5px);
  }
}

.section-header h3 {
  color: #2d3748;
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  position: relative;
}

.section-header h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 1px;
}

.info-grid {
  display: grid;
  gap: 20px;
}

.info-grid.three-columns {
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.info-item {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.info-icon {
  font-size: 1.2rem;
  margin-top: 2px;
  opacity: 0.8;
}

.info-content {
  flex: 1;
}

.info-content label {
  font-weight: 600;
  color: #4a5568;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
  display: block;
}

.info-content .info-value {
  color: #2d3748;
  font-size: 1.4rem;
  font-weight: 500;
  line-height: 1.5;
}

.info-value.face-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.face-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.face-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid #52c41a;
}

.face-status {
  font-size: 14px;
  font-weight: 500;
}

.face-status.success {
  color: #52c41a;
}

.face-status.error {
  color: #ff4d4f;
}

.face-error-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.face-error-info small {
  color: #999;
  font-size: 12px;
}

.face-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.no-face {
  color: #999;
  font-size: 14px;
}

.manage-face-btn {
  align-self: flex-start;
  padding: 6px 12px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.manage-face-btn:hover {
  background: #40a9ff;
}

/* 编辑按钮样式 */
.edit-btn {
  padding: 6px 12px;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.edit-btn:hover {
  background: #73d13d;
}

/* 编辑表单样式 */
.edit-form {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-item label {
  font-weight: 600;
  color: #4a5568;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.form-input {
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 18px;
  transition: all 0.3s;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.save-btn {
  padding: 12px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s;
}

.save-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-btn {
  padding: 12px 20px;
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s;
}

.cancel-btn:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

/* 角色标签样式 */
.role-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
}

.role-admin {
  background: #ff4d4f;
  color: white;
}

.role-teacher {
  background: #1890ff;
  color: white;
}

.role-student {
  background: #52c41a;
  color: white;
}

.role-default {
  background: #d9d9d9;
  color: #666;
}

.profile-actions {
  margin-top: 25px;
  text-align: center;
  width: 100%;
}

.btn {
  padding: 12px 28px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 180px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-secondary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: white;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-container p {
  font-size: 1.1rem;
  font-weight: 500;
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 220px 1fr;
    gap: 30px;
  }

  .info-grid.three-columns {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .user-profile {
    padding: 15px;
  }

  .profile-container {
    padding: 30px 25px;
    margin: 10px;
    border-radius: 20px;
  }

  .profile-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .profile-header h2 {
    font-size: 2rem;
  }

  .profile-header {
    margin-bottom: 35px;
  }

  .avatar-section {
    padding: 20px 15px;
    order: 1;
  }

  .avatar-container::after {
    display: none;
  }

  .info-section {
    padding: 20px 15px;
    order: 2;
  }

  .info-grid.three-columns {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .profile-actions {
    margin-top: 20px;
  }

  .btn {
    padding: 12px 28px;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: 25px 20px;
  }

  .profile-header h2 {
    font-size: 1.8rem;
  }

  .avatar-section,
  .info-section {
    padding: 20px 15px;
  }

  .info-item {
    padding: 15px;
  }

  .section-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 10px;
  }

  .section-header h3::after {
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
