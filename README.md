# 智慧实训室管理系统

基于Vue 3 + Ant Design Vue的智慧实训室管理系统，集成了人脸识别签到、课程管理、请假管理、安全监控等功能。

## 功能特性

### 🎯 核心功能

- **用户管理**: 支持学生、教师、管理员三种角色
- **课程管理**: 完整的课程信息管理，包括时间、地点、教师等
- **签到签退**: 基于人脸识别的智能签到系统
- **请假管理**: 在线请假申请和审批流程
- **签到统计**: 详细的签到数据统计和分析
- **安全监控**: 实训室安全事件识别和处理

### 🔧 技术特性

- **现代化UI**: 基于Ant Design Vue的美观界面
- **响应式设计**: 支持PC、平板、手机等多种设备
- **人脸识别**: 集成摄像头进行人脸识别签到
- **数据可视化**: 丰富的图表和统计展示
- **权限控制**: 基于角色的访问控制

## 系统架构

```
src/
├── components/          # Vue组件
│   ├── AttendanceCheck.vue      # 签到签退
│   ├── AttendanceRecords.vue    # 签到记录
│   ├── AttendanceStatistics.vue # 签到统计
│   ├── AttendanceCamera.vue     # 摄像头组件
│   ├── CourseManagement.vue     # 课程管理
│   ├── FaceCapture.vue          # 人脸采集
│   ├── FaceManagement.vue       # 人脸管理
│   ├── Home.vue                 # 首页
│   ├── Info.vue                 # 用户管理
│   ├── LeaveApplication.vue     # 请假管理
│   ├── Login.vue                # 登录页面
│   ├── SafetyMonitoring.vue     # 安全监控
│   └── UserProfile.vue          # 用户资料
├── router/              # 路由配置
├── utils/               # 工具函数
│   ├── api.js          # API接口
│   └── mockApi.js      # 模拟数据
└── database/            # 数据库脚本
    └── setup_with_data.sql
```

## 用户角色

### 👨‍🎓 学生功能
- 人脸识别签到签退
- 查看个人签到记录
- 在线请假申请
- 个人信息管理

### 👨‍🏫 教师功能
- 课程管理（增删改查）
- 生成签退码
- 调整学生签到状态
- 签到统计查看
- 请假申请审批
- 签到记录管理

### 👨‍💼 管理员功能
- 用户管理（学生、教师）
- 人脸信息管理
- 系统安全监控
- 全局数据统计

## 安装和运行

### 环境要求
- Node.js 16+
- MySQL 8.0+
- 现代浏览器（支持摄像头API）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd smart-lab-system
```

2. **安装依赖**
```bash
npm install
```

3. **数据库设置**
```bash
# 导入数据库结构和示例数据
mysql -u root -p < database/setup_with_data.sql
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问系统**
打开浏览器访问 `http://localhost:5173`

### 默认账户

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | 123456 | 系统管理员 |
| 教师 | teacher1 | 123456 | 张老师 |
| 教师 | teacher2 | 123456 | 李老师 |
| 学生 | student1 | 123456 | 王小明 |
| 学生 | student2 | 123456 | 李小红 |

## 功能说明

### 📚 课程管理
- 支持添加、编辑、删除课程
- 课程信息包括：课程名称、教师、教室、上课时间等
- 支持按教师、学期等条件筛选

### ✅ 签到管理
- **学生签到**: 输入学号 → 人脸识别 → 自动记录签到时间
- **学生签退**: 输入签退码 → 人脸识别 → 记录签退时间
- **教师功能**: 生成签退码、调整学生状态

### 📊 统计分析
- 按班级、时间范围统计签到情况
- 支持导出统计报表
- 可视化图表展示

### 🏥 请假管理
- 学生在线申请请假（病假、事假、公假）
- 教师审批请假申请
- 请假记录查询

### 🔒 安全监控
- 实时监控实训室安全状况
- 自动识别安全事件
- 事件处理和记录

## 数据库设计

### 主要数据表

- `user`: 用户信息表
- `course`: 课程信息表
- `attendance_record`: 签到记录表
- `leave_application`: 请假申请表
- `safety_event`: 安全事件表
- `monitoring_device`: 监控设备表

详细的数据库结构请参考 `database/setup_with_data.sql`

## 技术栈

### 前端
- **Vue 3**: 渐进式JavaScript框架
- **Ant Design Vue**: UI组件库
- **Vue Router**: 路由管理
- **Day.js**: 日期处理
- **Vite**: 构建工具

### 后端（待实现）
- **Node.js/Express**: 服务器框架
- **MySQL**: 数据库
- **JWT**: 身份认证
- **Multer**: 文件上传

### 人脸识别
- **WebRTC**: 浏览器摄像头API
- **Canvas API**: 图像处理
- **Face-api.js**: 人脸识别库（可选）

## 开发说明

### 项目结构说明
- 组件采用单文件组件（SFC）格式
- 使用Composition API编写
- 统一的样式风格和响应式设计
- 模块化的API接口管理

### 样式规范
- 使用渐变背景和圆角设计
- 统一的颜色主题（蓝色系）
- 响应式布局适配移动端
- 悬停效果和过渡动画

### 代码规范
- ESLint代码检查
- 组件命名采用PascalCase
- 方法命名采用camelCase
- 详细的注释说明

## 部署说明

### 生产环境构建
```bash
npm run build
```

### 服务器部署
1. 将构建后的文件部署到Web服务器
2. 配置数据库连接
3. 设置反向代理（如Nginx）
4. 配置HTTPS证书

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本系统目前为前端演示版本，人脸识别功能为模拟实现。在生产环境中需要集成真实的人脸识别服务和后端API。
