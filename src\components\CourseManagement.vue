<template>
  <div class="course-management">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="header-left">
          <a-button @click="goBack" class="back-btn">
            <arrow-left-outlined />
            返回
          </a-button>
          <h1>📚 课程管理</h1>
        </div>
        <div class="header-right">
          <a-button type="primary" @click="showAddModal" v-if="canManageCourses">
            <plus-outlined />
            添加课程
          </a-button>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card class="filter-card">
        <div class="filter-content">
          <div class="filter-row">
            <div class="filter-item">
              <label>学期</label>
              <a-select
                v-model:value="filters.semester"
                placeholder="选择学期"
                style="width: 180px"
                @change="loadCourses"
              >
                <a-select-option value="">全部学期</a-select-option>
                <a-select-option
                  v-for="semester in semesters"
                  :key="semester.semester"
                  :value="semester.semester"
                >
                  {{ semester.semester }} ({{ semester.academic_year }})
                </a-select-option>
              </a-select>
            </div>

            <div class="filter-item">
              <label>班级</label>
              <a-select
                v-model:value="filters.department"
                placeholder="选择班级"
                style="width: 200px"
                @change="loadCourses"
              >
                <a-select-option value="">全部班级</a-select-option>
                <a-select-option
                  v-for="dept in departments"
                  :key="dept"
                  :value="dept"
                >
                  {{ dept }}
                </a-select-option>
              </a-select>
            </div>

            <div class="filter-item">
              <label>周期</label>
              <a-select
                v-model:value="filters.week"
                placeholder="选择周期"
                style="width: 150px"
                @change="loadCourses"
              >
                <a-select-option value="">全部周期</a-select-option>
                <a-select-option
                  v-for="week in weekOptions"
                  :key="week"
                  :value="week"
                >
                  第{{ week }}周
                </a-select-option>
              </a-select>
            </div>

            <div class="filter-item">
              <label>课程类型</label>
              <a-select
                v-model:value="filters.courseType"
                placeholder="选择类型"
                style="width: 150px"
                @change="loadCourses"
              >
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option value="专业必修">📚 专业必修</a-select-option>
                <a-select-option value="专业选修">🎨 专业选修</a-select-option>
                <a-select-option value="实验课程">⚗️ 实验课程</a-select-option>
                <a-select-option value="体育课程">🏃 体育课程</a-select-option>
              </a-select>
            </div>

            <div class="filter-item">
              <a-button @click="resetFilters" class="reset-btn">
                <reload-outlined />
                重置
              </a-button>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总课程数"
              :value="courseStats.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <book-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="本周课程"
              :value="courseStats.thisWeek"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <calendar-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="冲突课程"
              :value="courseStats.conflicts"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃教师"
              :value="courseStats.teachers"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <user-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 课程表格视图切换 -->
    <div class="view-toggle">
      <a-radio-group v-model:value="viewMode" button-style="solid">
        <a-radio-button value="card">
          <appstore-outlined />
          卡片视图
        </a-radio-button>
        <a-radio-button value="table">
          <table-outlined />
          表格视图
        </a-radio-button>
        <a-radio-button value="schedule">
          <calendar-outlined />
          课程表视图
        </a-radio-button>
      </a-radio-group>
    </div>

    <!-- 课程内容区域 -->
    <div class="content-section">
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="card-view">
        <div v-if="loading" class="loading-container">
          <a-spin size="large" />
          <p>加载课程数据中...</p>
        </div>

        <div v-else-if="filteredCourses.length === 0" class="empty-container">
          <a-empty description="暂无课程数据" />
        </div>

        <div v-else class="courses-grid">
          <div
            v-for="course in filteredCourses"
            :key="course.id"
            class="course-card"
            :class="{ 'has-conflict': course.hasConflict }"
          >
            <div class="course-header">
              <div class="course-type-badge">
                {{ getCourseTypeEmoji(course.course_type) }}
              </div>
              <div class="course-actions" v-if="canManageCourses">
                <a-dropdown>
                  <a-button type="text" size="small">
                    <more-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="editCourse(course)">
                        <edit-outlined />
                        编辑
                      </a-menu-item>
                      <a-menu-item @click="deleteCourse(course)" danger>
                        <delete-outlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>

            <div class="course-content">
              <div class="course-title-section">
                <h3 class="course-name">{{ course.course_name }}</h3>
                <div class="course-code" v-if="course.course_code">
                  <a-tag color="blue" size="small">{{ course.course_code }}</a-tag>
                </div>
              </div>

              <div class="course-description" v-if="course.description">
                <p class="description-text">{{ course.description }}</p>
              </div>

              <div class="course-info">
                <div class="info-row">
                  <div class="info-item">
                    <environment-outlined />
                    <span>{{ course.classroom || '未指定教室' }}</span>
                  </div>
                  <div class="info-item">
                    <user-outlined />
                    <span>{{ course.teacher_name || '未指定教师' }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <clock-circle-outlined />
                    <span>{{ formatTime(course.start_time) }} - {{ formatTime(course.end_time) }}</span>
                  </div>
                  <div class="info-item">
                    <calendar-outlined />
                    <span>{{ getDayName(course.day_of_week) }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <team-outlined />
                    <span>{{ course.department || '未指定班级' }}</span>
                  </div>
                  <div class="info-item" v-if="course.weeks">
                    <schedule-outlined />
                    <span>第{{ course.weeks }}周</span>
                  </div>
                </div>

                <div class="info-row" v-if="course.semester || course.academic_year">
                  <div class="info-item" v-if="course.semester">
                    <book-outlined />
                    <span>{{ course.semester }}</span>
                  </div>
                  <div class="info-item" v-if="course.academic_year">
                    <calendar-outlined />
                    <span>{{ course.academic_year }}学年</span>
                  </div>
                </div>
              </div>

              <div v-if="course.hasConflict" class="conflict-warning">
                <warning-outlined />
                <span>时间冲突警告</span>
              </div>

              <div class="course-footer">
                <div class="footer-left">
                  <a-tag :color="getCourseTypeColor(course.course_type)" class="course-type-tag">
                    {{ getCourseTypeEmoji(course.course_type) }} {{ course.course_type || '未分类' }}
                  </a-tag>
                  <span class="course-credits" v-if="course.credits">
                    {{ course.credits }}学分
                  </span>
                </div>
                <div class="footer-right">
                  <a-button type="link" size="small" @click="viewCourseDetail(course)">
                    <eye-outlined />
                    详情
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="table-view">
        <a-table
          :columns="tableColumns"
          :data-source="filteredCourses"
          :loading="loading"
          :pagination="{ pageSize: 20, showSizeChanger: true }"
          row-key="id"
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'course_type'">
              <span>{{ getCourseTypeEmoji(record.course_type) }} {{ record.course_type }}</span>
            </template>
            <template v-if="column.key === 'time'">
              {{ formatTime(record.start_time) }} - {{ formatTime(record.end_time) }}
            </template>
            <template v-if="column.key === 'day_of_week'">
              {{ getDayName(record.day_of_week) }}
            </template>
            <template v-if="column.key === 'conflict'">
              <a-tag v-if="record.hasConflict" color="red">
                <warning-outlined />
                冲突
              </a-tag>
              <a-tag v-else color="green">正常</a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space v-if="canManageCourses">
                <a-button type="link" size="small" @click="editCourse(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" danger @click="deleteCourse(record)">
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 课程表视图 -->
      <div v-if="viewMode === 'schedule'" class="schedule-view">
        <div class="schedule-container">
          <div class="schedule-header">
            <div class="time-slot">时间</div>
            <div v-for="day in weekDays" :key="day.value" class="day-header">
              {{ day.label }}
            </div>
          </div>

          <div class="schedule-body">
            <div v-for="timeSlot in timeSlots" :key="timeSlot.id" class="schedule-row">
              <div class="time-slot">
                {{ timeSlot.start }} - {{ timeSlot.end }}
              </div>
              <div v-for="day in weekDays" :key="day.value" class="schedule-cell">
                <div
                  v-for="course in getCoursesForTimeSlot(day.value, timeSlot)"
                  :key="course.id"
                  class="schedule-course"
                  :class="{ 'has-conflict': course.hasConflict }"
                  @click="viewCourseDetail(course)"
                >
                  <div class="course-type-icon">{{ getCourseTypeEmoji(course.course_type) }}</div>
                  <div class="course-title">{{ course.course_name }}</div>
                  <div class="course-room">{{ course.classroom }}</div>
                  <div class="course-teacher">{{ course.teacher_name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑课程模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="editingCourse ? '编辑课程' : '添加课程'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <a-form
        :model="courseForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        ref="formRef"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="课程名称" name="course_name" :rules="[{ required: true, message: '请输入课程名称' }]">
              <a-input v-model:value="courseForm.course_name" placeholder="请输入课程名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="课程代码" name="course_code">
              <a-input v-model:value="courseForm.course_code" placeholder="请输入课程代码" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="授课教师" name="teacher_id">
              <a-select v-model:value="courseForm.teacher_id" placeholder="选择授课教师">
                <a-select-option
                  v-for="teacher in teachers"
                  :key="teacher.id"
                  :value="teacher.id"
                >
                  {{ teacher.username }} ({{ teacher.number }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="教室" name="classroom">
              <a-input v-model:value="courseForm.classroom" placeholder="请输入教室" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="星期" name="day_of_week" :rules="[{ required: true, message: '请选择星期' }]">
              <a-select v-model:value="courseForm.day_of_week" placeholder="选择星期">
                <a-select-option :value="1">周一</a-select-option>
                <a-select-option :value="2">周二</a-select-option>
                <a-select-option :value="3">周三</a-select-option>
                <a-select-option :value="4">周四</a-select-option>
                <a-select-option :value="5">周五</a-select-option>
                <a-select-option :value="6">周六</a-select-option>
                <a-select-option :value="7">周日</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="开始时间" name="start_time" :rules="[{ required: true, message: '请选择开始时间' }]">
              <a-time-picker
                v-model:value="courseForm.start_time"
                format="HH:mm"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="结束时间" name="end_time" :rules="[{ required: true, message: '请选择结束时间' }]">
              <a-time-picker
                v-model:value="courseForm.end_time"
                format="HH:mm"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="班级/部门" name="department">
              <a-input v-model:value="courseForm.department" placeholder="请输入班级或部门" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="学期" name="semester">
              <a-select v-model:value="courseForm.semester" placeholder="选择学期">
                <a-select-option
                  v-for="semester in semesters"
                  :key="semester.semester"
                  :value="semester.semester"
                >
                  {{ semester.semester }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="课程类型" name="course_type">
              <a-select v-model:value="courseForm.course_type" placeholder="选择课程类型">
                <a-select-option value="专业必修">📚 专业必修</a-select-option>
                <a-select-option value="专业选修">🎨 专业选修</a-select-option>
                <a-select-option value="实验课程">⚗️ 实验课程</a-select-option>
                <a-select-option value="体育课程">🏃 体育课程</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="学分" name="credits">
              <a-input-number
                v-model:value="courseForm.credits"
                :min="0"
                :max="10"
                :step="0.5"
                placeholder="学分"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="上课周次" name="weeks">
              <a-input v-model:value="courseForm.weeks" placeholder="如: 1-16" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="课程描述" name="description">
          <a-textarea
            v-model:value="courseForm.description"
            :rows="3"
            placeholder="请输入课程描述"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 课程详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="课程详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedCourse" class="course-detail">
        <div class="detail-header">
          <h2>{{ selectedCourse.course_name }}</h2>
          <a-tag :color="getCourseTypeColor(selectedCourse.course_type)">
            {{ getCourseTypeEmoji(selectedCourse.course_type) }} {{ selectedCourse.course_type }}
          </a-tag>
        </div>

        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="课程代码">{{ selectedCourse.course_code || '未设置' }}</a-descriptions-item>
          <a-descriptions-item label="授课教师">{{ selectedCourse.teacher_name || '未指定' }}</a-descriptions-item>
          <a-descriptions-item label="教室">{{ selectedCourse.classroom || '未指定' }}</a-descriptions-item>
          <a-descriptions-item label="上课时间">
            {{ getDayName(selectedCourse.day_of_week) }} {{ formatTime(selectedCourse.start_time) }}-{{ formatTime(selectedCourse.end_time) }}
          </a-descriptions-item>
          <a-descriptions-item label="班级/部门">{{ selectedCourse.department || '未指定' }}</a-descriptions-item>
          <a-descriptions-item label="学期">{{ selectedCourse.semester || '未指定' }}</a-descriptions-item>
          <a-descriptions-item label="学分">{{ selectedCourse.credits || '未设置' }}</a-descriptions-item>
          <a-descriptions-item label="上课周次">{{ selectedCourse.weeks || '未设置' }}</a-descriptions-item>
          <a-descriptions-item label="课程描述" :span="2">
            {{ selectedCourse.description || '暂无描述' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  ReloadOutlined,
  BookOutlined,
  CalendarOutlined,
  WarningOutlined,
  UserOutlined,
  AppstoreOutlined,
  TableOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  ScheduleOutlined,
  EyeOutlined
} from '@ant-design/icons-vue';
import {
  Select as ASelect,
  SelectOption as ASelectOption,
  Button as AButton,
  Card as ACard,
  Row as ARow,
  Col as ACol,
  Statistic as AStatistic,
  RadioGroup as ARadioGroup,
  RadioButton as ARadioButton,
  Spin as ASpin,
  Empty as AEmpty,
  Dropdown as ADropdown,
  Menu as AMenu,
  MenuItem as AMenuItem,
  Table as ATable,
  Modal as AModal,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Textarea as ATextarea,
  TimePicker as ATimePicker,
  Tag as ATag,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Space as ASpace,
  message
} from 'ant-design-vue';
import { authAPI, authUtils } from '../utils/api';
import dayjs from 'dayjs';

const router = useRouter();

// 响应式数据
const loading = ref(true);
const courses = ref([]);
const semesters = ref([]);
const departments = ref([]);
const teachers = ref([]);
const viewMode = ref('card');
const modalVisible = ref(false);
const detailModalVisible = ref(false);
const editingCourse = ref(null);
const selectedCourse = ref(null);
const submitting = ref(false);
const formRef = ref();

// 筛选条件
const filters = reactive({
  semester: '',
  department: '',
  week: '',
  courseType: ''
});

// 课程表单
const courseForm = reactive({
  course_name: '',
  course_code: '',
  teacher_id: null,
  classroom: '',
  start_time: null,
  end_time: null,
  day_of_week: null,
  department: '',
  semester: '',
  course_type: '',
  credits: null,
  weeks: '',
  description: ''
});

// 当前用户
const currentUser = computed(() => authUtils.getCurrentUser());

// 权限检查
const canManageCourses = computed(() => {
  const user = currentUser.value;
  return user && (user.role === 'admin' || user.role === 'teacher');
});

// 周期选项
const weekOptions = computed(() => {
  const weeks = [];
  for (let i = 1; i <= 20; i++) {
    weeks.push(i);
  }
  return weeks;
});

// 星期选项
const weekDays = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
  { value: 7, label: '周日' }
];

// 时间段配置
const timeSlots = [
  { id: 1, start: '08:00', end: '09:40' },
  { id: 2, start: '10:00', end: '11:40' },
  { id: 3, start: '14:00', end: '15:40' },
  { id: 4, start: '16:00', end: '17:40' },
  { id: 5, start: '19:00', end: '20:40' }
];

// 表格列配置
const tableColumns = [
  {
    title: '课程名称',
    dataIndex: 'course_name',
    key: 'course_name',
    width: 150
  },
  {
    title: '课程类型',
    dataIndex: 'course_type',
    key: 'course_type',
    width: 120
  },
  {
    title: '教师',
    dataIndex: 'teacher_name',
    key: 'teacher_name',
    width: 100
  },
  {
    title: '教室',
    dataIndex: 'classroom',
    key: 'classroom',
    width: 100
  },
  {
    title: '时间',
    key: 'time',
    width: 120
  },
  {
    title: '星期',
    key: 'day_of_week',
    width: 80
  },
  {
    title: '班级',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '学期',
    dataIndex: 'semester',
    key: 'semester',
    width: 100
  },
  {
    title: '冲突状态',
    key: 'conflict',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
];

// 过滤后的课程列表
const filteredCourses = computed(() => {
  let result = courses.value;

  if (filters.semester) {
    result = result.filter(course => course.semester === filters.semester);
  }

  if (filters.department) {
    result = result.filter(course => course.department === filters.department);
  }

  if (filters.week) {
    result = result.filter(course => {
      if (!course.weeks) return false;
      const weekRange = course.weeks.split('-');
      if (weekRange.length === 2) {
        const start = parseInt(weekRange[0]);
        const end = parseInt(weekRange[1]);
        const week = parseInt(filters.week);
        return week >= start && week <= end;
      }
      return course.weeks.includes(filters.week);
    });
  }

  if (filters.courseType) {
    result = result.filter(course => course.course_type === filters.courseType);
  }

  return result;
});

// 课程统计
const courseStats = computed(() => {
  const total = courses.value.length;
  const thisWeek = courses.value.filter(course => {
    if (!course.weeks) return false;
    const currentWeek = Math.ceil((new Date() - new Date('2024-09-01')) / (7 * 24 * 60 * 60 * 1000));
    const weekRange = course.weeks.split('-');
    if (weekRange.length === 2) {
      const start = parseInt(weekRange[0]);
      const end = parseInt(weekRange[1]);
      return currentWeek >= start && currentWeek <= end;
    }
    return false;
  }).length;

  const conflicts = courses.value.filter(course => course.hasConflict).length;
  const teacherSet = new Set(courses.value.map(course => course.teacher_id).filter(id => id));

  return {
    total,
    thisWeek,
    conflicts,
    teachers: teacherSet.size
  };
});

// 工具方法
const getCourseTypeEmoji = (type) => {
  const emojiMap = {
    '专业必修': '📚',
    '专业选修': '🎨',
    '实验课程': '⚗️',
    '体育课程': '🏃'
  };
  return emojiMap[type] || '📖';
};

const getCourseTypeColor = (type) => {
  const colorMap = {
    '专业必修': 'blue',
    '专业选修': 'green',
    '实验课程': 'orange',
    '体育课程': 'purple'
  };
  return colorMap[type] || 'default';
};

const getDayName = (dayOfWeek) => {
  const dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  return dayNames[dayOfWeek] || '';
};

const formatTime = (time) => {
  if (!time) return '';
  if (typeof time === 'string') {
    return time.substring(0, 5);
  }
  return dayjs(time).format('HH:mm');
};

// 检查课程冲突
const checkConflicts = () => {
  courses.value.forEach(course => {
    course.hasConflict = false;
    const conflicts = courses.value.filter(other =>
      other.id !== course.id &&
      other.department === course.department &&
      other.day_of_week === course.day_of_week &&
      other.semester === course.semester &&
      isTimeOverlap(course.start_time, course.end_time, other.start_time, other.end_time)
    );
    course.hasConflict = conflicts.length > 0;
  });
};

const isTimeOverlap = (start1, end1, start2, end2) => {
  const s1 = dayjs(`2000-01-01 ${start1}`);
  const e1 = dayjs(`2000-01-01 ${end1}`);
  const s2 = dayjs(`2000-01-01 ${start2}`);
  const e2 = dayjs(`2000-01-01 ${end2}`);

  return s1.isBefore(e2) && s2.isBefore(e1);
};

// 获取指定时间段的课程
const getCoursesForTimeSlot = (dayOfWeek, timeSlot) => {
  return filteredCourses.value.filter(course => {
    if (course.day_of_week !== dayOfWeek) return false;

    const courseStart = dayjs(`2000-01-01 ${course.start_time}`);
    const courseEnd = dayjs(`2000-01-01 ${course.end_time}`);
    const slotStart = dayjs(`2000-01-01 ${timeSlot.start}`);
    const slotEnd = dayjs(`2000-01-01 ${timeSlot.end}`);

    return courseStart.isBefore(slotEnd) && courseEnd.isAfter(slotStart);
  });
};

// 事件处理方法
const goBack = () => {
  router.push('/home');
};

const showAddModal = () => {
  editingCourse.value = null;
  resetForm();
  modalVisible.value = true;
};

const editCourse = (course) => {
  editingCourse.value = course;
  Object.assign(courseForm, {
    ...course,
    start_time: course.start_time ? dayjs(course.start_time, 'HH:mm:ss') : null,
    end_time: course.end_time ? dayjs(course.end_time, 'HH:mm:ss') : null
  });
  modalVisible.value = true;
};

const deleteCourse = async (course) => {
  try {
    await authAPI.deleteCourse(course.id);
    message.success('课程删除成功');
    loadCourses();
  } catch (error) {
    console.error('删除课程失败:', error);
    message.error(error.response?.data?.error || '删除失败');
  }
};

const viewCourseDetail = (course) => {
  selectedCourse.value = course;
  detailModalVisible.value = true;
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    submitting.value = true;

    const formData = {
      ...courseForm,
      start_time: courseForm.start_time ? courseForm.start_time.format('HH:mm:ss') : null,
      end_time: courseForm.end_time ? courseForm.end_time.format('HH:mm:ss') : null
    };

    if (editingCourse.value) {
      await authAPI.updateCourse(editingCourse.value.id, formData);
      message.success('课程更新成功');
    } else {
      await authAPI.createCourse(formData);
      message.success('课程创建成功');
    }

    modalVisible.value = false;
    loadCourses();
  } catch (error) {
    console.error('提交失败:', error);
    message.error(error.response?.data?.error || '操作失败');
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(courseForm, {
    course_name: '',
    course_code: '',
    teacher_id: null,
    classroom: '',
    start_time: null,
    end_time: null,
    day_of_week: null,
    department: '',
    semester: '',
    course_type: '',
    credits: null,
    weeks: '',
    description: ''
  });
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

const resetFilters = () => {
  Object.assign(filters, {
    semester: '',
    department: '',
    week: '',
    courseType: ''
  });
  loadCourses();
};

// 数据加载方法
const loadCourses = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams();

    if (filters.semester) params.append('semester', filters.semester);
    if (filters.department) params.append('department', filters.department);

    const response = await authAPI.getCourses(Object.fromEntries(params));
    courses.value = response.courses || [];
    checkConflicts();
  } catch (error) {
    console.error('加载课程失败:', error);
    message.error('加载课程数据失败');
  } finally {
    loading.value = false;
  }
};

const loadSemesters = async () => {
  try {
    const response = await authAPI.getSemesters();
    semesters.value = response.semesters || [];
  } catch (error) {
    console.error('加载学期数据失败:', error);
  }
};

const loadDepartments = async () => {
  try {
    const response = await authAPI.getDepartments();
    departments.value = response.departments || [];
  } catch (error) {
    console.error('加载部门数据失败:', error);
  }
};

const loadTeachers = async () => {
  try {
    const response = await authAPI.getUsers({ role: 'teacher' });
    teachers.value = response.users || [];
  } catch (error) {
    console.error('加载教师数据失败:', error);
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadCourses();
  loadSemesters();
  loadDepartments();
  loadTeachers();
});
</script>

<style scoped>
.course-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 头部样式 */
.header-section {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  color: white;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.back-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  border-radius: 15px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.filter-content {
  padding: 10px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.reset-btn {
  margin-top: 22px;
  border-radius: 8px;
}

/* 统计区域样式 */
.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 15px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* 视图切换样式 */
.view-toggle {
  margin-bottom: 20px;
  text-align: center;
}

/* 内容区域样式 */
.content-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 加载和空状态样式 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loading-container p {
  margin-top: 15px;
  font-size: 16px;
}

/* 卡片视图样式 */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  padding: 10px 0;
}

.course-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.course-card.has-conflict {
  border-color: #ff4d4f;
  background: linear-gradient(135deg, #fff2f0 0%, #ffffff 100%);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.course-type-badge {
  font-size: 24px;
  padding: 8px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 10px;
}

.course-content h3 {
  margin: 0 0 15px 0;
  color: #1890ff;
  font-size: 18px;
  font-weight: 600;
}

.course-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.info-item .anticon {
  color: #1890ff;
  font-size: 16px;
}

.conflict-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff4d4f;
  background: #fff2f0;
  padding: 8px 12px;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: 500;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-credits {
  color: #666;
  font-size: 14px;
}

/* 课程表视图样式 */
.schedule-view {
  overflow-x: auto;
}

.schedule-container {
  min-width: 800px;
  border: 1px solid #e8e8e8;
  border-radius: 10px;
  overflow: hidden;
}

.schedule-header {
  display: grid;
  grid-template-columns: 100px repeat(7, 1fr);
  background: #f5f5f5;
}

.schedule-header .time-slot,
.schedule-header .day-header {
  padding: 15px 10px;
  text-align: center;
  font-weight: 600;
  border-right: 1px solid #e8e8e8;
}

.schedule-body {
  display: flex;
  flex-direction: column;
}

.schedule-row {
  display: grid;
  grid-template-columns: 100px repeat(7, 1fr);
  border-bottom: 1px solid #e8e8e8;
}

.schedule-row:last-child {
  border-bottom: none;
}

.schedule-row .time-slot {
  padding: 20px 10px;
  text-align: center;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  font-weight: 500;
  color: #666;
}

.schedule-cell {
  padding: 10px;
  border-right: 1px solid #e8e8e8;
  min-height: 80px;
  position: relative;
}

.schedule-course {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  padding: 8px;
  border-radius: 8px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.schedule-course:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.schedule-course.has-conflict {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}

.course-type-icon {
  font-size: 14px;
  margin-bottom: 2px;
}

.course-title {
  font-weight: 600;
  margin-bottom: 2px;
  line-height: 1.2;
}

.course-room,
.course-teacher {
  font-size: 11px;
  opacity: 0.9;
  line-height: 1.1;
}

/* 模态框样式优化 */
:deep(.ant-modal-content) {
  border-radius: 15px;
  overflow: hidden;
}

:deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

:deep(.ant-modal-title) {
  color: white;
  font-weight: 600;
}

:deep(.ant-modal-close) {
  color: white;
}

:deep(.ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

/* 表单样式优化 */
:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #333;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-time-picker),
:deep(.ant-input-number),
:deep(.ant-textarea) {
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-time-picker:focus),
:deep(.ant-input-number:focus),
:deep(.ant-textarea:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式优化 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background: #f5f5f5;
  font-weight: 600;
  color: #333;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f0f9ff;
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 8px;
}

/* 统计卡片样式优化 */
:deep(.ant-statistic-title) {
  font-weight: 600;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-management {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item {
    width: 100%;
  }

  .courses-grid {
    grid-template-columns: 1fr;
  }

  .schedule-container {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .header-left h1 {
    font-size: 20px;
  }

  .course-card {
    padding: 15px;
  }

  .schedule-container {
    min-width: 500px;
  }
}
</style>