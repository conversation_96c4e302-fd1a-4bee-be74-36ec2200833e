<template>
  <div class="leave-application">
    <div class="header">
      <h2>请假管理</h2>
      <a-button type="primary" @click="showApplyModal" v-if="isStudent">
        <PlusOutlined />
        申请请假
      </a-button>
    </div>

    <!-- 筛选条件 -->
    <div class="filters" v-if="!isStudent">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select
            v-model:value="filters.status"
            placeholder="选择状态"
            allowClear
            style="width: 100%"
            @change="loadApplications"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="pending">待审批</a-select-option>
            <a-select-option value="approved">已批准</a-select-option>
            <a-select-option value="rejected">已拒绝</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button @click="resetFilters">重置筛选</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 请假申请列表 -->
    <a-table
      :columns="columns"
      :data-source="applications"
      :loading="loading"
      :pagination="false"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'leave_type'">
          <a-tag :color="getLeaveTypeColor(record.leave_type)">
            {{ getLeaveTypeText(record.leave_type) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'duration'">
          {{ record.start_date }} 至 {{ record.end_date }}
          <div v-if="record.start_time && record.end_time" style="font-size: 12px; color: #666;">
            {{ record.start_time }} - {{ record.end_time }}
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button 
              type="link" 
              size="small" 
              @click="viewApplication(record)"
            >
              查看
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="approveApplication(record, 'approve')"
              v-if="canApprove(record)"
            >
              批准
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              danger
              @click="approveApplication(record, 'reject')"
              v-if="canApprove(record)"
            >
              拒绝
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 申请请假模态框 -->
    <a-modal
      v-model:open="applyModalVisible"
      title="申请请假"
      @ok="handleApply"
      @cancel="handleApplyCancel"
      :confirm-loading="submitting"
      width="600px"
    >
      <a-form
        ref="applyFormRef"
        :model="applyForm"
        :rules="applyFormRules"
        layout="vertical"
      >
        <a-form-item label="请假类型" name="leave_type">
          <a-select v-model:value="applyForm.leave_type" placeholder="请选择请假类型">
            <a-select-option value="sick_leave">病假</a-select-option>
            <a-select-option value="personal_leave">事假</a-select-option>
            <a-select-option value="official_leave">公假</a-select-option>
          </a-select>
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开始日期" name="start_date">
              <a-date-picker 
                v-model:value="applyForm.start_date" 
                style="width: 100%" 
                :disabled-date="disabledDate"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束日期" name="end_date">
              <a-date-picker 
                v-model:value="applyForm.end_date" 
                style="width: 100%" 
                :disabled-date="disabledDate"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开始时间" name="start_time">
              <a-time-picker 
                v-model:value="applyForm.start_time" 
                format="HH:mm" 
                style="width: 100%" 
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束时间" name="end_time">
              <a-time-picker 
                v-model:value="applyForm.end_time" 
                format="HH:mm" 
                style="width: 100%" 
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="请假原因" name="reason">
          <a-textarea 
            v-model:value="applyForm.reason" 
            placeholder="请详细说明请假原因" 
            :rows="4" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情模态框 -->
    <a-modal
      v-model:open="viewModalVisible"
      title="请假申请详情"
      :footer="null"
      width="600px"
    >
      <div v-if="viewingApplication">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="申请人">
            {{ viewingApplication.student_name }}
          </a-descriptions-item>
          <a-descriptions-item label="学号">
            {{ viewingApplication.student_number }}
          </a-descriptions-item>
          <a-descriptions-item label="请假类型">
            <a-tag :color="getLeaveTypeColor(viewingApplication.leave_type)">
              {{ getLeaveTypeText(viewingApplication.leave_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(viewingApplication.status)">
              {{ getStatusText(viewingApplication.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="开始日期">
            {{ viewingApplication.start_date }}
          </a-descriptions-item>
          <a-descriptions-item label="结束日期">
            {{ viewingApplication.end_date }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间" v-if="viewingApplication.start_time">
            {{ viewingApplication.start_time }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间" v-if="viewingApplication.end_time">
            {{ viewingApplication.end_time }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间" :span="2">
            {{ viewingApplication.created_at }}
          </a-descriptions-item>
          <a-descriptions-item label="请假原因" :span="2">
            {{ viewingApplication.reason }}
          </a-descriptions-item>
          <a-descriptions-item label="审批人" v-if="viewingApplication.approver_name">
            {{ viewingApplication.approver_name }}
          </a-descriptions-item>
          <a-descriptions-item label="审批时间" v-if="viewingApplication.approved_at">
            {{ viewingApplication.approved_at }}
          </a-descriptions-item>
          <a-descriptions-item label="审批意见" :span="2" v-if="viewingApplication.approval_comment">
            {{ viewingApplication.approval_comment }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 审批模态框 -->
    <a-modal
      v-model:open="approveModalVisible"
      :title="approveAction === 'approve' ? '批准请假' : '拒绝请假'"
      @ok="handleApprove"
      @cancel="approveModalVisible = false"
      :confirm-loading="approving"
    >
      <a-form layout="vertical">
        <a-form-item label="审批意见">
          <a-textarea 
            v-model:value="approveComment" 
            placeholder="请输入审批意见（可选）" 
            :rows="3" 
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { authUtils } from '../utils/api'
import dayjs from 'dayjs'

export default {
  name: 'LeaveApplication',
  components: {
    PlusOutlined
  },
  setup() {
    const loading = ref(false)
    const submitting = ref(false)
    const approving = ref(false)
    const applications = ref([])
    const applyModalVisible = ref(false)
    const viewModalVisible = ref(false)
    const approveModalVisible = ref(false)
    const viewingApplication = ref(null)
    const approvingApplication = ref(null)
    const approveAction = ref('')
    const approveComment = ref('')
    
    const applyFormRef = ref()

    const currentUser = computed(() => authUtils.getCurrentUser())
    const isStudent = computed(() => {
      return currentUser.value && currentUser.value.role === 'student'
    })

    const filters = reactive({
      status: ''
    })

    const applyForm = reactive({
      leave_type: '',
      start_date: null,
      end_date: null,
      start_time: null,
      end_time: null,
      reason: ''
    })

    const applyFormRules = {
      leave_type: [{ required: true, message: '请选择请假类型' }],
      start_date: [{ required: true, message: '请选择开始日期' }],
      end_date: [{ required: true, message: '请选择结束日期' }],
      reason: [{ required: true, message: '请输入请假原因' }]
    }

    const columns = computed(() => {
      const baseColumns = [
        { title: '请假类型', key: 'leave_type', width: 100 },
        { title: '请假时间', key: 'duration', width: 200 },
        { title: '原因', dataIndex: 'reason', key: 'reason', ellipsis: true },
        { title: '状态', key: 'status', width: 100 },
        { title: '申请时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
        { title: '操作', key: 'action', width: 150 }
      ]

      if (!isStudent.value) {
        baseColumns.unshift(
          { title: '申请人', dataIndex: 'student_name', key: 'student_name', width: 100 },
          { title: '学号', dataIndex: 'student_number', key: 'student_number', width: 120 }
        )
      }

      return baseColumns
    })

    const getLeaveTypeColor = (type) => {
      const colors = {
        sick_leave: 'red',
        personal_leave: 'orange',
        official_leave: 'blue'
      }
      return colors[type] || 'default'
    }

    const getLeaveTypeText = (type) => {
      const texts = {
        sick_leave: '病假',
        personal_leave: '事假',
        official_leave: '公假'
      }
      return texts[type] || type
    }

    const getStatusColor = (status) => {
      const colors = {
        pending: 'orange',
        approved: 'green',
        rejected: 'red'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待审批',
        approved: '已批准',
        rejected: '已拒绝'
      }
      return texts[status] || status
    }

    const disabledDate = (current) => {
      // 不能选择过去的日期
      return current && current < dayjs().startOf('day')
    }

    const canApprove = (record) => {
      return !isStudent.value && record.status === 'pending'
    }

    // 加载请假申请列表
    const loadApplications = async () => {
      loading.value = true
      try {
        const params = new URLSearchParams()
        if (filters.status) params.append('status', filters.status)

        const response = await fetch(`/api/leave/applications?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          applications.value = data.applications
        } else {
          const error = await response.json()
          message.error(error.error || '加载请假申请失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        loading.value = false
      }
    }

    const showApplyModal = () => {
      resetApplyForm()
      applyModalVisible.value = true
    }

    const resetApplyForm = () => {
      Object.assign(applyForm, {
        leave_type: '',
        start_date: null,
        end_date: null,
        start_time: null,
        end_time: null,
        reason: ''
      })
    }

    const handleApply = async () => {
      try {
        await applyFormRef.value.validate()
        submitting.value = true

        const formData = {
          ...applyForm,
          start_date: applyForm.start_date ? applyForm.start_date.format('YYYY-MM-DD') : null,
          end_date: applyForm.end_date ? applyForm.end_date.format('YYYY-MM-DD') : null,
          start_time: applyForm.start_time ? applyForm.start_time.format('HH:mm') : null,
          end_time: applyForm.end_time ? applyForm.end_time.format('HH:mm') : null
        }

        const response = await fetch('/api/leave/apply', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify(formData)
        })

        if (response.ok) {
          message.success('请假申请提交成功')
          applyModalVisible.value = false
          loadApplications()
        } else {
          const error = await response.json()
          message.error(error.error || '申请失败')
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        submitting.value = false
      }
    }

    const handleApplyCancel = () => {
      applyModalVisible.value = false
      resetApplyForm()
    }

    const viewApplication = (application) => {
      viewingApplication.value = application
      viewModalVisible.value = true
    }

    const approveApplication = (application, action) => {
      approvingApplication.value = application
      approveAction.value = action
      approveComment.value = ''
      approveModalVisible.value = true
    }

    const handleApprove = async () => {
      approving.value = true
      try {
        const response = await fetch(`/api/leave/applications/${approvingApplication.value.id}/approve`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify({
            action: approveAction.value,
            comment: approveComment.value
          })
        })

        if (response.ok) {
          message.success(approveAction.value === 'approve' ? '请假申请已批准' : '请假申请已拒绝')
          approveModalVisible.value = false
          loadApplications()
        } else {
          const error = await response.json()
          message.error(error.error || '操作失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        approving.value = false
      }
    }

    const resetFilters = () => {
      filters.status = ''
      loadApplications()
    }

    onMounted(() => {
      loadApplications()
    })

    return {
      loading,
      submitting,
      approving,
      applications,
      applyModalVisible,
      viewModalVisible,
      approveModalVisible,
      viewingApplication,
      approvingApplication,
      approveAction,
      approveComment,
      applyFormRef,
      currentUser,
      isStudent,
      filters,
      applyForm,
      applyFormRules,
      columns,
      getLeaveTypeColor,
      getLeaveTypeText,
      getStatusColor,
      getStatusText,
      disabledDate,
      canApprove,
      loadApplications,
      showApplyModal,
      handleApply,
      handleApplyCancel,
      viewApplication,
      approveApplication,
      handleApprove,
      resetFilters
    }
  }
}
</script>

<style scoped>
.leave-application {
  padding: 24px;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 50%, #f6ffed 100%);
  min-height: calc(100vh - 80px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  background: white;
  padding: 20px 24px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.header h2 {
  color: #1890ff;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.filters {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 标签页样式优化 */
:deep(.ant-tabs) {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

:deep(.ant-tabs-tab) {
  font-weight: 500;
  font-size: 15px;
}

:deep(.ant-tabs-tab-active) {
  color: #1890ff;
}

:deep(.ant-tabs-ink-bar) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  height: 3px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background: white;
}

:deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e8e8e8;
  font-size: 15px;
}

:deep(.ant-table-tbody > tr > td) {
  font-size: 14px;
  padding: 16px 12px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
}

/* 按钮样式优化 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 表单样式优化 */
:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker),
:deep(.ant-range-picker),
:deep(.ant-textarea) {
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
  font-size: 15px;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker-focused),
:deep(.ant-range-picker-focused),
:deep(.ant-textarea:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 8px;
  font-weight: 500;
  padding: 6px 12px;
  font-size: 13px;
  border: none;
}

/* 模态框样式优化 */
:deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

:deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

:deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

:deep(.ant-modal-close) {
  color: white;
}

:deep(.ant-modal-body) {
  padding: 24px;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .leave-application {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 20px;
    margin-bottom: 24px;
  }

  .header h2 {
    font-size: 20px;
  }

  .filters {
    padding: 20px;
    margin-bottom: 24px;
  }

  :deep(.ant-tabs) {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .leave-application {
    padding: 12px;
  }

  .header {
    padding: 16px;
  }

  .header h2 {
    font-size: 18px;
  }

  .filters {
    padding: 16px;
  }

  :deep(.ant-tabs) {
    padding: 16px;
  }
}
</style>
