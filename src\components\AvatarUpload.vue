<template>
  <div class="avatar-upload">
    <div class="avatar-preview" @click="triggerFileInput">
      <img 
        :src="previewUrl || getUserAvatarUrl()" 
        :alt="user?.username + '的头像'"
        class="avatar-img"
        @error="handleAvatarError"
      />
      <div class="avatar-overlay">
        <i class="upload-icon">📷</i>
        <span>点击上传</span>
      </div>
    </div>
    
    <input 
      ref="fileInput"
      type="file" 
      accept="image/png,image/jpg,image/jpeg,image/gif,image/webp"
      @change="handleFileSelect"
      style="display: none;"
    />
    
    <div class="avatar-actions">
      <button 
        @click="triggerFileInput" 
        class="btn btn-primary"
        :disabled="uploading"
      >
        {{ uploading ? '上传中...' : '选择头像' }}
      </button>
      
      <button 
        @click="deleteAvatar" 
        class="btn btn-danger"
        :disabled="uploading || !user?.avatar"
        v-if="user?.avatar"
      >
        删除头像
      </button>
    </div>
    
    <div class="upload-tips">
      <p>支持 PNG、JPG、JPEG、GIF、WEBP 格式</p>
      <p>文件大小不超过 5MB</p>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
    <!-- 成功提示 -->
    <div v-if="successMessage" class="success-message">
      {{ successMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { authAPI, authUtils } from '../utils/api';

// Props
const props = defineProps({
  user: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['avatar-updated']);

// 响应式数据
const fileInput = ref(null);
const previewUrl = ref('');
const uploading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// 获取用户头像URL
const getUserAvatarUrl = () => {
  if (props.user && props.user.avatar) {
    return authAPI.getAvatarUrl(props.user.avatar);
  }
  return new URL('../assets/avatar/default-avatar.svg', import.meta.url).href;
};

// 头像加载错误处理
const handleAvatarError = (event) => {
  event.target.src = new URL('../assets/avatar/default-avatar.svg', import.meta.url).href;
};

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click();
};

// 显示消息
const showMessage = (message, type = 'success') => {
  if (type === 'success') {
    successMessage.value = message;
    errorMessage.value = '';
  } else {
    errorMessage.value = message;
    successMessage.value = '';
  }
  
  setTimeout(() => {
    successMessage.value = '';
    errorMessage.value = '';
  }, 3000);
};

// 文件选择处理
const handleFileSelect = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 文件类型检查
  const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    showMessage('不支持的文件格式，请上传 PNG、JPG、JPEG、GIF 或 WEBP 格式的图片', 'error');
    return;
  }
  
  // 文件大小检查 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    showMessage('文件大小不能超过5MB', 'error');
    return;
  }
  
  // 创建预览
  const reader = new FileReader();
  reader.onload = (e) => {
    previewUrl.value = e.target.result;
  };
  reader.readAsDataURL(file);
  
  // 上传文件
  await uploadFile(file);
};

// 上传文件
const uploadFile = async (file) => {
  uploading.value = true;
  
  try {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await authAPI.uploadAvatar(formData);
    const { avatar, message } = response.data;
    
    // 更新用户信息
    const currentUser = authUtils.getCurrentUser();
    if (currentUser) {
      currentUser.avatar = avatar;
      authUtils.setAuth(authUtils.getToken(), currentUser);
    }
    
    showMessage(message);
    emit('avatar-updated', avatar);
    
    // 清除预览
    previewUrl.value = '';
    
  } catch (error) {
    const errorMsg = error.response?.data?.error || '头像上传失败，请重试';
    showMessage(errorMsg, 'error');
    previewUrl.value = ''; // 清除预览
  } finally {
    uploading.value = false;
    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = '';
    }
  }
};

// 删除头像
const deleteAvatar = async () => {
  if (!confirm('确定要删除头像吗？')) return;
  
  uploading.value = true;
  
  try {
    const response = await authAPI.deleteAvatar();
    const { message } = response.data;
    
    // 更新用户信息
    const currentUser = authUtils.getCurrentUser();
    if (currentUser) {
      currentUser.avatar = null;
      authUtils.setAuth(authUtils.getToken(), currentUser);
    }
    
    showMessage(message);
    emit('avatar-updated', null);
    
  } catch (error) {
    const errorMsg = error.response?.data?.error || '头像删除失败，请重试';
    showMessage(errorMsg, 'error');
  } finally {
    uploading.value = false;
  }
};
</script>

<style scoped>
.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 15px 10px;
}

.avatar-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #667eea, #764ba2) border-box;
  transition: all 0.4s ease;
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.2);
}

.avatar-preview:hover {
  transform: scale(1.08);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
}

.avatar-preview:hover .avatar-overlay {
  opacity: 1;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: all 0.3s ease;
}

.avatar-preview:hover .avatar-img {
  transform: scale(1.1);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  color: white;
  font-size: 13px;
  font-weight: 500;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

.avatar-actions {
  display: flex;
  flex-direction: row;
  gap: 12px;
  width: 100%;
  max-width: 280px;
  justify-content: center;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex: 1;
  text-align: center;
  min-width: 100px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled::before {
  display: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.upload-tips {
  text-align: center;
  font-size: 13px;
  color: #718096;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.6);
  padding: 15px 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.upload-tips p {
  margin: 0;
}

.upload-tips p:first-child {
  margin-bottom: 4px;
}

.error-message {
  color: #e53e3e;
  background: linear-gradient(135deg, #fed7d7, #feb2b2);
  padding: 15px 20px;
  border-radius: 12px;
  border: 1px solid #fc8181;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.2);
  animation: shake 0.5s ease-in-out;
}

.success-message {
  color: #38a169;
  background: linear-gradient(135deg, #c6f6d5, #9ae6b4);
  padding: 15px 20px;
  border-radius: 12px;
  border: 1px solid #68d391;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.2);
  animation: slideIn 0.5s ease-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .avatar-actions {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    max-width: none;
    gap: 10px;
  }

  .btn {
    flex: 1;
    min-width: 100px;
    max-width: 140px;
  }
}

@media (max-width: 480px) {
  .avatar-preview {
    width: 110px;
    height: 110px;
  }

  .avatar-actions {
    flex-direction: column;
    width: 100%;
    max-width: 200px;
    gap: 8px;
  }

  .btn {
    flex: none;
    width: 100%;
    min-width: auto;
    padding: 8px 16px;
    font-size: 12px;
  }

  .upload-tips {
    font-size: 11px;
    padding: 10px 12px;
  }
}
</style>
