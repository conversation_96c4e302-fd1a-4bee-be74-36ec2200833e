<template>
  <div class="safety-monitoring">
    <div class="header">
      <h2>实训室安全监控</h2>
      <a-space>
        <a-button type="primary" @click="refreshData" :loading="loading">
          <reload-outlined />
          刷新数据
        </a-button>
        <a-button @click="showSettingsModal">
          <setting-outlined />
          监控设置
        </a-button>
      </a-space>
    </div>

    <!-- 安全状态概览 -->
    <div class="status-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="安全状态"
              :value="safetyStatus.status"
              :value-style="{ color: getSafetyStatusColor(safetyStatus.status) }"
            >
              <template #prefix>
                <safety-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日事件"
              :value="safetyStatus.todayEvents"
              :value-style="{ color: safetyStatus.todayEvents > 0 ? '#cf1322' : '#3f8600' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="在线设备"
              :value="safetyStatus.onlineDevices"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <video-camera-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="监控覆盖率"
              :value="safetyStatus.coverageRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <aim-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 监控视图切换 -->
    <div class="view-selection">
      <h3>分区视图</h3>
    </div>

    <!-- 分区视图 -->
    <div class="zones-view">
      <div class="zones-content">
        <!-- 左侧分区监控 -->
        <div class="zones-left">
          <!-- 实验室A监控区 -->
          <div class="monitoring-zone">
            <div class="zone-header">
              <div class="zone-title-wrapper">
                <h3><experiment-outlined /> 实验室A监控</h3>
                <a-button size="small" type="primary" @click="showDeviceModal('A')">查看设备</a-button>
              </div>
              <a-tag :color="getZoneStatusColor('area1')">
                {{ getZoneStatus('area1') }}
              </a-tag>
            </div>
            <div class="zone-cameras">
              <div v-for="camera in getCamerasByArea('area1')" :key="camera.id" class="camera-card">
                <div class="camera-header">
                  <span class="camera-name">{{ camera.name }}</span>
                  <a-tag :color="camera.online ? 'green' : 'red'">
                    {{ camera.online ? '在线' : '离线' }}
                  </a-tag>
                </div>
                <div class="camera-feed">
                  <img :src="camera.feedUrl" alt="监控画面" class="camera-image" />
                  <div v-if="!camera.online" class="offline-overlay">
                    <disconnect-outlined />
                    <p>设备离线</p>
                  </div>
                  <div v-if="camera.alert" class="alert-overlay">
                    <warning-outlined />
                    <p>{{ camera.alertMessage }}</p>
                  </div>
                </div>
                <div class="camera-footer">
                  <a-space>
                    <a-button size="small" @click="viewFullscreen(camera)">
                      <fullscreen-outlined />
                    </a-button>
                    <a-button size="small" @click="viewEvents(camera)">
                      <history-outlined />
                      事件记录
                    </a-button>
                  </a-space>
                </div>
              </div>
            </div>
          </div>

          <!-- 实验室B监控区 -->
          <div class="monitoring-zone">
            <div class="zone-header">
              <div class="zone-title-wrapper">
                <h3><experiment-outlined /> 实验室B监控</h3>
                <a-button size="small" type="primary" @click="showDeviceModal('B')">查看设备</a-button>
              </div>
              <a-tag :color="getZoneStatusColor('area2')">
                {{ getZoneStatus('area2') }}
              </a-tag>
            </div>
            <div class="zone-cameras">
              <div v-for="camera in getCamerasByArea('area2')" :key="camera.id" class="camera-card">
                <div class="camera-header">
                  <span class="camera-name">{{ camera.name }}</span>
                  <a-tag :color="camera.online ? 'green' : 'red'">
                    {{ camera.online ? '在线' : '离线' }}
                  </a-tag>
                </div>
                <div class="camera-feed">
                  <img :src="camera.feedUrl" alt="监控画面" class="camera-image" />
                  <div v-if="!camera.online" class="offline-overlay">
                    <disconnect-outlined />
                    <p>设备离线</p>
                  </div>
                  <div v-if="camera.alert" class="alert-overlay">
                    <warning-outlined />
                    <p>{{ camera.alertMessage }}</p>
                  </div>
                </div>
                <div class="camera-footer">
                  <a-space>
                    <a-button size="small" @click="viewFullscreen(camera)">
                      <fullscreen-outlined />
                    </a-button>
                    <a-button size="small" @click="viewEvents(camera)">
                      <history-outlined />
                      事件记录
                    </a-button>
                  </a-space>
                </div>
              </div>
            </div>
          </div>

          <!-- 实验室C监控区 -->
          <div class="monitoring-zone">
            <div class="zone-header">
              <div class="zone-title-wrapper">
                <h3><experiment-outlined /> 实验室C监控</h3>
                <a-button size="small" type="primary" @click="showDeviceModal('C')">查看设备</a-button>
              </div>
              <a-tag :color="getZoneStatusColor('area3')">
                {{ getZoneStatus('area3') }}
              </a-tag>
            </div>
            <div class="zone-cameras">
              <div v-for="camera in getCamerasByArea('area3')" :key="camera.id" class="camera-card">
                <div class="camera-header">
                  <span class="camera-name">{{ camera.name }}</span>
                  <a-tag :color="camera.online ? 'green' : 'red'">
                    {{ camera.online ? '在线' : '离线' }}
                  </a-tag>
                </div>
                <div class="camera-feed">
                  <img :src="camera.feedUrl" alt="监控画面" class="camera-image" />
                  <div v-if="!camera.online" class="offline-overlay">
                    <disconnect-outlined />
                    <p>设备离线</p>
                  </div>
                  <div v-if="camera.alert" class="alert-overlay">
                    <warning-outlined />
                    <p>{{ camera.alertMessage }}</p>
                  </div>
                </div>
                <div class="camera-footer">
                  <a-space>
                    <a-button size="small" @click="viewFullscreen(camera)">
                      <fullscreen-outlined />
                    </a-button>
                    <a-button size="small" @click="viewEvents(camera)">
                      <history-outlined />
                      事件记录
                    </a-button>
                  </a-space>
                </div>
              </div>
            </div>
          </div>

          <!-- 走廊监控区 -->
          <div class="monitoring-zone">
            <div class="zone-header">
              <h3><environment-outlined /> 走廊监控</h3>
              <a-tag :color="getZoneStatusColor('area4')">
                {{ getZoneStatus('area4') }}
              </a-tag>
            </div>
            <div class="zone-cameras">
              <div v-for="camera in getCamerasByArea('area4')" :key="camera.id" class="camera-card">
                <div class="camera-header">
                  <span class="camera-name">{{ camera.name }}</span>
                  <a-tag :color="camera.online ? 'green' : 'red'">
                    {{ camera.online ? '在线' : '离线' }}
                  </a-tag>
                </div>
                <div class="camera-feed">
                  <img :src="camera.feedUrl" alt="监控画面" class="camera-image" />
                  <div v-if="!camera.online" class="offline-overlay">
                    <disconnect-outlined />
                    <p>设备离线</p>
                  </div>
                  <div v-if="camera.alert" class="alert-overlay">
                    <warning-outlined />
                    <p>{{ camera.alertMessage }}</p>
                  </div>
                </div>
                <div class="camera-footer">
                  <a-space>
                    <a-button size="small" @click="viewFullscreen(camera)">
                      <fullscreen-outlined />
                    </a-button>
                    <a-button size="small" @click="viewEvents(camera)">
                      <history-outlined />
                      事件记录
                    </a-button>
                  </a-space>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧安全事件记录 -->
        <div class="zones-right">
          <div class="events-section">
            <div class="section-header">
              <h3>安全事件记录</h3>
              <a-button @click="loadEvents" :loading="loadingEvents" size="small">
                <ReloadOutlined />
                刷新
              </a-button>
            </div>

            <!-- 事件筛选器 -->
            <div class="event-filters">
              <select
                v-model="eventFilters.type"
                @change="loadEvents"
                class="event-type-select"
              >
                <option value="">全部类型</option>
                <option value="unauthorized_access">未授权访问</option>
                <option value="equipment_misuse">设备误用</option>
                <option value="fire_hazard">火灾隐患</option>
                <option value="electrical_hazard">电气隐患</option>
                <option value="chemical_hazard">化学品隐患</option>
                <option value="physical_hazard">物理隐患</option>
              </select>
              <a-range-picker
                v-model:value="eventFilters.dateRange"
                style="width: 100%"
                @change="loadEvents"
                :getPopupContainer="getPopupContainer"
              />
            </div>

            <!-- 事件列表 -->
            <div class="events-list">
              <a-spin :spinning="loadingEvents">
                <div v-if="safetyEvents.length === 0" class="empty-events">
                  <warning-outlined class="empty-icon" />
                  <p>暂无安全事件</p>
                </div>

                <div v-else class="event-items">
                  <div
                    v-for="event in safetyEvents"
                    :key="event.id"
                    class="event-item"
                    :class="getEventClass(event)"
                    @click="viewEventDetails(event)"
                  >
                    <div class="event-header">
                      <a-tag :color="getEventTypeColor(event.type)" size="small">
                        {{ getEventTypeText(event.type) }}
                      </a-tag>
                      <a-tag :color="getEventStatusColor(event.status)" size="small">
                        {{ getEventStatusText(event.status) }}
                      </a-tag>
                    </div>

                    <div class="event-info">
                      <div class="event-location">
                        <span class="location-text">{{ event.location }}</span>
                        <span class="device-text">{{ event.deviceName }}</span>
                      </div>
                      <div class="event-time">
                        {{ formatTime(event.timestamp) }}
                      </div>
                    </div>

                    <div class="event-description">
                      {{ event.description }}
                    </div>

                    <div class="event-actions" @click.stop>
                      <a-space size="small">
                        <a-button type="link" size="small" @click="viewEventDetails(event)">
                          详情
                        </a-button>
                        <a-button
                          v-if="event.status === 'pending'"
                          type="link"
                          size="small"
                          @click="handleEvent(event)"
                        >
                          处理
                        </a-button>
                      </a-space>
                    </div>
                  </div>
                </div>
              </a-spin>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备状态查看弹窗 -->
    <a-modal
      v-model:open="deviceModalVisible"
      :title="`实验室${selectedAreaForDevices}电脑设备状态`"
      width="1000px"
      :footer="null"
      @cancel="deviceModalVisible = false"
    >
      <div class="device-status-container">
        <div class="device-grid">
          <div 
            v-for="device in labDevices" 
            :key="device.id" 
            class="device-item"
            :class="device.status"
          >
            <div class="device-icon">
              <desktop-outlined />
            </div>
            <div class="device-info">
              <div class="device-number">{{ device.number }}</div>
              <div class="device-status">{{ device.statusText }}</div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  SettingOutlined,
  SafetyOutlined,
  WarningOutlined,
  VideoCameraOutlined,
  AimOutlined,
  DisconnectOutlined,
  FullscreenOutlined,
  HistoryOutlined,
  ExperimentOutlined,
  EnvironmentOutlined,
  DesktopOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

export default {
  name: 'SafetyMonitoring',
  components: {
    ReloadOutlined,
    SettingOutlined,
    SafetyOutlined,
    WarningOutlined,
    VideoCameraOutlined,
    AimOutlined,
    DisconnectOutlined,
    FullscreenOutlined,
    HistoryOutlined,
    ExperimentOutlined,
    EnvironmentOutlined,
    DesktopOutlined
  },
  setup() {
    const loading = ref(false)
    const loadingEvents = ref(false)
    const savingSettings = ref(false)
    const handlingEvent = ref(false)
    const settingsModalVisible = ref(false)
    const eventDetailModalVisible = ref(false)
    const handleEventModalVisible = ref(false)

    const selectedArea = ref('all')
    const selectedEvent = ref(null)
    const refreshTimer = ref(null)
    const viewMode = ref('zones') // 新增：视图模式

    // 安全状态数据
    const safetyStatus = ref({
      status: '正常',
      todayEvents: 0,
      onlineDevices: 8,
      coverageRate: 95
    })

    // 摄像头数据
    const cameras = ref([
      {
        id: 1,
        name: '实训室A-入口',
        area: 'area1',
        online: true,
        alert: false,
        alertMessage: '',
        feedUrl: 'src\\assets\\camera\\1 (1).jpg'
      },
      {
        id: 2,
        name: '实训室A-工作区',
        area: 'area1',
        online: true,
        alert: true,
        alertMessage: '检测到电脑未关机',
        feedUrl: 'src\\assets\\camera\\1 (2).jpg'
      },
      {
        id: 3,
        name: '实训室B-入口',
        area: 'area2',
        online: true,
        alert: false,
        alertMessage: '',
        feedUrl: 'src\\assets\\camera\\1 (3).jpg'
      },
      {
        id: 4,
        name: '实训室B-设备区',
        area: 'area2',
        online: false,
        alert: false,
        alertMessage: '',
        feedUrl: '/api/camera/feed/4'
      },
      {
        id: 5,
        name: '实训室C-入口',
        area: 'area3',
        online: true,
        alert: false,
        alertMessage: '',
        feedUrl: 'src\\assets\\camera\\1 (1).png'
      },
      {
        id: 6,
        name: '实训室C',
        area: 'area3',
        online: true,
        alert: false,
        alertMessage: '',
        feedUrl: 'src\\assets\\camera\\1 (1).webp'
      },
      {
        id: 7,
        name: '走廊监控',
        area: 'area4',
        online: true,
        alert: false,
        alertMessage: '',
        feedUrl: 'src\\assets\\camera\\1.jpg'
      },
    ])

    // 安全事件数据
    const safetyEvents = ref([
      {
        id: 1,
        type: 'unauthorized_access',
        timestamp: '2024-01-20 14:30:00',
        location: '实训室A',
        deviceName: '摄像头A-02',
        description: '检测到电脑未关机、空闲机房',
        status: 'pending',
        imageUrl: 'src\\assets\\camera\\1 (2).jpg'
      },
      {
        id: 2,
        type: 'equipment_misuse',
        timestamp: '2024-01-20 13:15:00',
        location: '实训室B',
        deviceName: '摄像头B-01',
        description: '异常闯入报警',
        status: 'resolved',
        handledBy: '张老师',
        handledAt: '2024-01-20 13:20:00',
        resolution: '已提醒人员离开',
        imageUrl: '/api/events/image/2'
      },
      {
        id: 3,
        type: 'fire_hazard',
        timestamp: '2024-01-20 11:45:00',
        location: '实训室C',
        deviceName: '摄像头C-02',
        description: '检测到烟雾异常',
        status: 'false_alarm',
        handledBy: '李老师',
        handledAt: '2024-01-20 11:50:00',
        resolution: '确认为实验产生的正常烟雾',
        imageUrl: '/api/events/image/3'
      }
    ])

    // 事件筛选
    const eventFilters = reactive({
      type: '',
      dateRange: null
    })

    // 设置
    const settings = reactive({
      refreshInterval: 10,
      notifyOnEvent: true,
      soundAlert: true,
      displayMode: 'grid'
    })

    // 事件处理表单
    const eventHandlingForm = reactive({
      resolution: '',
      status: ''
    })

    // 计算属性：过滤后的摄像头
    const filteredCameras = computed(() => {
      if (selectedArea.value === 'all') {
        return cameras.value
      }
      return cameras.value.filter(camera => camera.area === selectedArea.value)
    })

    // 新增：根据区域获取摄像头
    const getCamerasByArea = (area) => {
      return cameras.value.filter(camera => camera.area === area)
    }

    // 新增：获取区域状态
    const getZoneStatus = (area) => {
      const zoneCameras = getCamerasByArea(area)
      const onlineCameras = zoneCameras.filter(camera => camera.online)
      const alertCameras = zoneCameras.filter(camera => camera.alert)
      
      if (alertCameras.length > 0) {
        return '有警报'
      } else if (onlineCameras.length === zoneCameras.length) {
        return '正常'
      } else if (onlineCameras.length > 0) {
        return '部分离线'
      } else {
        return '全部离线'
      }
    }

    // 新增：获取区域状态颜色
    const getZoneStatusColor = (area) => {
      const status = getZoneStatus(area)
      const colors = {
        '正常': 'green',
        '有警报': 'red',
        '部分离线': 'orange',
        '全部离线': 'red'
      }
      return colors[status] || 'default'
    }

    // 新增：处理视图模式变化
    const handleViewModeChange = () => {
      // 可以在这里添加视图模式切换的逻辑
    }

    // 表格列定义
    const eventColumns = [
      { title: '事件ID', dataIndex: 'id', key: 'id', width: 80 },
      { title: '事件类型', key: 'type', width: 120 },
      { title: '发生时间', key: 'timestamp', width: 150 },
      { title: '位置', dataIndex: 'location', key: 'location', width: 100 },
      { title: '设备', dataIndex: 'deviceName', key: 'deviceName', width: 120 },
      { title: '状态', key: 'status', width: 100 },
      { title: '操作', key: 'action', width: 150, fixed: 'right' }
    ]

    // 获取安全状态颜色
    const getSafetyStatusColor = (status) => {
      const colors = {
        '正常': '#52c41a',
        '警告': '#faad14',
        '危险': '#f5222d'
      }
      return colors[status] || '#d9d9d9'
    }

    // 获取事件类型颜色
    const getEventTypeColor = (type) => {
      const colors = {
        unauthorized_access: 'red',
        equipment_misuse: 'orange',
        fire_hazard: 'red',
        electrical_hazard: 'volcano',
        chemical_hazard: 'purple',
        physical_hazard: 'magenta'
      }
      return colors[type] || 'default'
    }

    // 获取事件类型文本
    const getEventTypeText = (type) => {
      const texts = {
        unauthorized_access: '电脑未关机',
        equipment_misuse: '设备误用',
        fire_hazard: '火灾隐患',
        electrical_hazard: '电气隐患',
        chemical_hazard: '化学品隐患',
        physical_hazard: '物理隐患'
      }
      return texts[type] || type
    }

    // 获取事件状态颜色
    const getEventStatusColor = (status) => {
      const colors = {
        pending: 'orange',
        resolved: 'green',
        false_alarm: 'blue',
        escalated: 'red'
      }
      return colors[status] || 'default'
    }

    // 获取事件状态文本
    const getEventStatusText = (status) => {
      const texts = {
        pending: '待处理',
        resolved: '已解决',
        false_alarm: '误报',
        escalated: '已上报'
      }
      return texts[status] || status
    }

    // 格式化日期时间
    const formatDateTime = (dateTimeStr) => {
      if (!dateTimeStr) return ''
      try {
        return dayjs(dateTimeStr).format('YYYY-MM-DD HH:mm:ss')
      } catch (error) {
        console.warn('日期格式化错误:', error)
        return dateTimeStr
      }
    }

    // 格式化时间（仅时间部分）
    const formatTime = (dateTimeStr) => {
      if (!dateTimeStr) return ''
      try {
        return dayjs(dateTimeStr).format('HH:mm:ss')
      } catch (error) {
        console.warn('时间格式化错误:', error)
        return dateTimeStr
      }
    }

    // 获取事件样式类
    const getEventClass = (event) => {
      const classes = ['event-item']
      if (event.status === 'pending') {
        classes.push('pending')
      } else if (event.status === 'resolved') {
        classes.push('resolved')
      }
      return classes.join(' ')
    }

    // 刷新数据
    const refreshData = async () => {
      loading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 更新安全状态
        safetyStatus.value.todayEvents = Math.floor(Math.random() * 5)
        safetyStatus.value.onlineDevices = 7 + Math.floor(Math.random() * 2)

        message.success('数据刷新成功')
      } catch (error) {
        message.error('数据刷新失败')
      } finally {
        loading.value = false
      }
    }

    // 处理区域变化
    const handleAreaChange = () => {
      // 可以在这里添加区域切换的逻辑
    }

    // 全屏查看
    const viewFullscreen = (camera) => {
      message.info(`全屏查看 ${camera.name}`)
    }

    // 查看事件
    const viewEvents = (camera) => {
      message.info(`查看 ${camera.name} 的事件记录`)
    }

    // 加载事件
    const loadEvents = () => {
      loadingEvents.value = true
      setTimeout(() => {
        loadingEvents.value = false
      }, 500)
    }

    // 查看事件详情
    const viewEventDetails = (event) => {
      selectedEvent.value = event
      eventDetailModalVisible.value = true
    }

    // 处理事件
    const handleEvent = (event) => {
      selectedEvent.value = event
      eventHandlingForm.resolution = ''
      eventHandlingForm.status = ''
      handleEventModalVisible.value = true
    }

    // 提交事件处理
    const submitEventHandling = async () => {
      if (!eventHandlingForm.resolution || !eventHandlingForm.status) {
        message.error('请填写完整的处理信息')
        return
      }

      handlingEvent.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 更新事件状态
        const eventIndex = safetyEvents.value.findIndex(e => e.id === selectedEvent.value.id)
        if (eventIndex !== -1) {
          safetyEvents.value[eventIndex].status = eventHandlingForm.status
          safetyEvents.value[eventIndex].resolution = eventHandlingForm.resolution
          safetyEvents.value[eventIndex].handledBy = '当前用户'
          safetyEvents.value[eventIndex].handledAt = dayjs().format('YYYY-MM-DD HH:mm:ss')
        }

        message.success('事件处理成功')
        handleEventModalVisible.value = false
        eventDetailModalVisible.value = false
      } catch (error) {
        message.error('事件处理失败')
      } finally {
        handlingEvent.value = false
      }
    }

    // 显示设置模态框
    const showSettingsModal = () => {
      settingsModalVisible.value = true
    }

    // 保存设置
    const saveSettings = async () => {
      savingSettings.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        // 重新设置定时器
        if (refreshTimer.value) {
          clearInterval(refreshTimer.value)
        }
        refreshTimer.value = setInterval(refreshData, settings.refreshInterval * 1000)

        message.success('设置保存成功')
        settingsModalVisible.value = false
      } catch (error) {
        message.error('设置保存失败')
      } finally {
        savingSettings.value = false
      }
    }

    // 显示设备模态框
    const deviceModalVisible = ref(false)
    const selectedAreaForDevices = ref('')

    const showDeviceModal = (area) => {
      console.log('点击查看设备按钮，区域：', area)
      selectedAreaForDevices.value = area
      deviceModalVisible.value = true
      console.log('弹窗状态：', deviceModalVisible.value)
    }

    // 设备状态数据（60台电脑，3台关机，1台长时间关机，其余开机）
    const labDevices = ref([
      { id: 1, number: '01', status: 'online', statusText: '开机' },
      { id: 2, number: '02', status: 'online', statusText: '开机' },
      { id: 3, number: '03', status: 'online', statusText: '开机' },
      { id: 4, number: '04', status: 'online', statusText: '开机' },
      { id: 5, number: '05', status: 'online', statusText: '开机' },
      { id: 6, number: '06', status: 'online', statusText: '开机' },
      { id: 7, number: '07', status: 'online', statusText: '开机' },
      { id: 8, number: '08', status: 'online', statusText: '开机' },
      { id: 9, number: '09', status: 'online', statusText: '开机' },
      { id: 10, number: '10', status: 'online', statusText: '开机' },
      { id: 11, number: '11', status: 'online', statusText: '开机' },
      { id: 12, number: '12', status: 'online', statusText: '开机' },
      { id: 13, number: '13', status: 'online', statusText: '开机' },
      { id: 14, number: '14', status: 'online', statusText: '开机' },
      { id: 15, number: '15', status: 'online', statusText: '开机' },
      { id: 16, number: '16', status: 'online', statusText: '开机' },
      { id: 17, number: '17', status: 'online', statusText: '开机' },
      { id: 18, number: '18', status: 'online', statusText: '开机' },
      { id: 19, number: '19', status: 'online', statusText: '开机' },
      { id: 20, number: '20', status: 'online', statusText: '开机' },
      { id: 21, number: '21', status: 'online', statusText: '开机' },
      { id: 22, number: '22', status: 'online', statusText: '开机' },
      { id: 23, number: '23', status: 'online', statusText: '开机' },
      { id: 24, number: '24', status: 'online', statusText: '开机' },
      { id: 25, number: '25', status: 'online', statusText: '开机' },
      { id: 26, number: '26', status: 'online', statusText: '开机' },
      { id: 27, number: '27', status: 'online', statusText: '开机' },
      { id: 28, number: '28', status: 'online', statusText: '开机' },
      { id: 29, number: '29', status: 'online', statusText: '开机' },
      { id: 30, number: '30', status: 'online', statusText: '开机' },
      { id: 31, number: '31', status: 'online', statusText: '开机' },
      { id: 32, number: '32', status: 'online', statusText: '开机' },
      { id: 33, number: '33', status: 'online', statusText: '开机' },
      { id: 34, number: '34', status: 'online', statusText: '开机' },
      { id: 35, number: '35', status: 'online', statusText: '开机' },
      { id: 36, number: '36', status: 'online', statusText: '开机' },
      { id: 37, number: '37', status: 'online', statusText: '开机' },
      { id: 38, number: '38', status: 'online', statusText: '开机' },
      { id: 39, number: '39', status: 'online', statusText: '开机' },
      { id: 40, number: '40', status: 'online', statusText: '开机' },
      { id: 41, number: '41', status: 'online', statusText: '开机' },
      { id: 42, number: '42', status: 'online', statusText: '开机' },
      { id: 43, number: '43', status: 'online', statusText: '开机' },
      { id: 44, number: '44', status: 'online', statusText: '开机' },
      { id: 45, number: '45', status: 'online', statusText: '开机' },
      { id: 46, number: '46', status: 'online', statusText: '开机' },
      { id: 47, number: '47', status: 'online', statusText: '开机' },
      { id: 48, number: '48', status: 'online', statusText: '开机' },
      { id: 49, number: '49', status: 'online', statusText: '开机' },
      { id: 50, number: '50', status: 'online', statusText: '开机' },
      { id: 51, number: '51', status: 'online', statusText: '开机' },
      { id: 52, number: '52', status: 'online', statusText: '开机' },
      { id: 53, number: '53', status: 'online', statusText: '开机' },
      { id: 54, number: '54', status: 'online', statusText: '开机' },
      { id: 55, number: '55', status: 'online', statusText: '开机' },
      { id: 56, number: '56', status: 'online', statusText: '开机' },
      { id: 57, number: '57', status: 'offline', statusText: '关机' },
      { id: 58, number: '58', status: 'offline', statusText: '关机' },
      { id: 59, number: '59', status: 'offline', statusText: '关机' },
      { id: 60, number: '60', status: 'long-offline', statusText: '长时间关机' }
    ])

    // 安全的弹窗容器获取函数
    const getPopupContainer = (node) => {
      if (typeof document !== 'undefined' && document.body) {
        return node?.parentNode || document.body
      }
      return node?.parentNode || null
    }

    // 组件挂载
    onMounted(() => {
      refreshData()
      refreshTimer.value = setInterval(refreshData, settings.refreshInterval * 1000)
    })

    // 组件卸载
    onUnmounted(() => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value)
      }
    })

    return {
      loading,
      loadingEvents,
      savingSettings,
      handlingEvent,
      settingsModalVisible,
      eventDetailModalVisible,
      handleEventModalVisible,
      selectedArea,
      selectedEvent,
      viewMode, // 新增
      safetyStatus,
      cameras,
      safetyEvents,
      eventFilters,
      settings,
      eventHandlingForm,
      filteredCameras,
      eventColumns,
      getSafetyStatusColor,
      getEventTypeColor,
      getEventTypeText,
      getEventStatusColor,
      getEventStatusText,
      formatDateTime,
      formatTime,
      getEventClass,
      getCamerasByArea, // 新增
      getZoneStatus, // 新增
      getZoneStatusColor, // 新增
      handleViewModeChange, // 新增
      refreshData,
      handleAreaChange,
      viewFullscreen,
      viewEvents,
      loadEvents,
      viewEventDetails,
      handleEvent,
      submitEventHandling,
      showSettingsModal,
      saveSettings,
      deviceModalVisible,
      selectedAreaForDevices,
      showDeviceModal,
      labDevices,
      getPopupContainer // 新增
    }
  }
}
</script>

<style scoped>
.safety-monitoring {
  padding: 24px;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 50%, #fff2e8 100%);
  min-height: calc(100vh - 80px);
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

/* 状态概览样式 */
.status-overview {
  margin-bottom: 24px;
}

/* 区域选择样式 */
.area-selection {
  margin-bottom: 24px;
  text-align: center;
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 视图切换样式 */
.view-selection {
  margin-bottom: 24px;
  text-align: center;
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 分区视图样式 */
.zones-view {
  height: calc(100vh - 300px);
}

.zones-content {
  display: flex;
  gap: 24px;
  height: 100%;
}

.zones-left {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  padding-right: 12px;
}

.zones-right {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  min-width: 350px;
  display: flex;
  flex-direction: column;
}

.monitoring-zone {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.monitoring-zone:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.zone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.zone-header h3 {
  margin: 0;
  color: #1890ff;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.zone-header h3 .anticon {
  font-size: 24px;
}

.zone-title-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 查看设备按钮样式 - 使用:deep()穿透样式 */
:deep(.zone-title-wrapper .ant-btn) {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%) !important;
  border: 2px solid #1890ff !important;
  border-radius: 20px !important;
  padding: 4px 16px !important;
  height: 28px !important;
  line-height: 20px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
  color: white !important;
  min-width: 70px !important;
}

:deep(.zone-title-wrapper .ant-btn:hover) {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;
  border-color: #096dd9 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4) !important;
  color: white !important;
}

:deep(.zone-title-wrapper .ant-btn:active),
:deep(.zone-title-wrapper .ant-btn:focus) {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%) !important;
  border-color: #0050b3 !important;
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3) !important;
  color: white !important;
}

.zone-cameras {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

/* 视频网格样式 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

.camera-feed {
  position: relative;
  height: 180px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.offline-overlay,
.alert-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.offline-overlay {
  background: rgba(0, 0, 0, 0.8);
}

.alert-overlay {
  background: rgba(255, 77, 79, 0.8);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

.offline-overlay .anticon,
.alert-overlay .anticon {
  font-size: 32px;
  margin-bottom: 8px;
}

.camera-footer {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e8e8e8;
}

/* 事件区域样式 */
.events-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h3 {
  margin: 0;
  color: #1890ff;
  font-size: 16px;
  font-weight: 600;
}

/* 事件筛选器 */
.event-filters {
  margin-bottom: 16px;
}

/* 事件列表 */
.events-list {
  flex: 1;
  overflow-y: auto;
}

.empty-events {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  font-size: 32px;
  color: #d9d9d9;
  margin-bottom: 12px;
}

.event-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.event-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.event-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  background: #f0f8ff;
}

.event-item.pending {
  border-left: 4px solid #faad14;
  background: #fffbe6;
}

.event-item.resolved {
  border-left: 4px solid #52c41a;
  background: #f6ffed;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-location {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.location-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.device-text {
  color: #8c8c8c;
  font-size: 12px;
}

.event-time {
  color: #8c8c8c;
  font-size: 12px;
  text-align: right;
}

.event-description {
  color: #595959;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-actions {
  display: flex;
  justify-content: flex-end;
}

/* 事件详情样式 */
.event-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.event-image {
  text-align: center;
}

.event-image img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.event-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

/* 统计卡片样式优化 */
:deep(.ant-card) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

:deep(.ant-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 700;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e8e8e8;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f0f8ff;
}

/* 按钮样式优化 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 单选按钮组样式 */
:deep(.ant-radio-button-wrapper) {
  border-radius: 6px;
  margin: 0 4px;
  border: 2px solid #d9d9d9;
  transition: all 0.3s;
}

:deep(.ant-radio-button-wrapper-checked) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #1890ff;
  color: white;
}

/* 模态框样式优化 */
:deep(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

:deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

:deep(.ant-modal-close) {
  color: white;
}

:deep(.ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

/* 统一视图样式保持原样 */
.unified-view .area-selection {
  margin-bottom: 24px;
  text-align: center;
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 主要内容区域 - 左右布局 */
.main-content {
  display: flex;
  gap: 24px;
  height: calc(100vh - 300px);
}

/* 左侧监控视频区域 */
.left-section {
  flex: 2;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

/* 右侧事件记录区域 */
.right-section {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  min-width: 350px;
  display: flex;
  flex-direction: column;
}

.camera-card {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
}

.camera-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
}

.camera-name {
  font-weight: 600;
  color: #333;
}

.camera-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 分区视图中的摄像头网格 - 设置卡片固定宽度 */
.zones-view .monitoring-zone .zone-cameras {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, 320px) !important;
  gap: 16px !important;
  justify-content: start !important;
}

/* 分区视图中的卡片宽度 */
.zones-view .camera-card {
  width: 320px !important;
  max-width: 320px !important;
  min-width: 320px !important;
}

/* 统一视图中的视频网格 - 使用更高优先级 */
.unified-view .left-section .video-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)) !important;
  gap: 16px !important;
}

/* 通用网格样式 - 备用方案 */
.zone-cameras {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

/* 视频网格样式 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

/* 完全重置下拉选择器样式，确保正确渲染 */
:deep(.ant-select) {
  display: block !important;
  width: 100% !important;
  position: relative !important;
}

:deep(.ant-select-selector) {
  display: flex !important;
  align-items: center !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  padding: 4px 11px !important;
  background: white !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
}

:deep(.ant-select-selector:hover) {
  border-color: #40a9ff !important;
}

:deep(.ant-select-selection-placeholder) {
  color: #bfbfbf !important;
}

:deep(.ant-select-selection-item) {
  color: rgba(0, 0, 0, 0.85) !important;
}

:deep(.ant-select-arrow) {
  right: 11px !important;
  color: rgba(0, 0, 0, 0.25) !important;
}

:deep(.ant-select-dropdown) {
  position: absolute !important;
  z-index: 9999 !important;
  background: white !important;
  border-radius: 6px !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #d9d9d9 !important;
}

:deep(.ant-select-item) {
  padding: 5px 12px !important;
  cursor: pointer !important;
  color: rgba(0, 0, 0, 0.85) !important;
  background: white !important;
  border: none !important;
  display: block !important;
}

:deep(.ant-select-item:hover) {
  background: #f5f5f5 !important;
}

:deep(.ant-select-item-option-selected) {
  background: #e6f7ff !important;
  color: #1890ff !important;
}

/* 隐藏可能导致问题的选项内容 */
:deep(.ant-select-item-option-content) {
  display: block !important;
}

/* 确保事件筛选器容器样式正确 */
.event-filters {
  margin-bottom: 16px !important;
  width: 100% !important;
  overflow: visible !important;
}

/* 强制重置可能影响选择器的全局样式 */
.zones-right * {
  box-sizing: border-box !important;
}

/* 如果仍有问题，强制隐藏选项文字 */
.zones-right .ant-select .ant-select-item-option-content {
  display: none !important;
}

.zones-right .ant-select .ant-select-selection-item {
  display: block !important;
}

/* 原生select样式，替代有问题的ant-select */
.event-type-select {
  width: 100% !important;
  height: 32px !important;
  padding: 4px 11px !important;
  margin-bottom: 12px !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  background: white !important;
  font-size: 14px !important;
  line-height: 1.5715 !important;
  color: rgba(0, 0, 0, 0.85) !important;
  transition: all 0.3s !important;
  cursor: pointer !important;
  outline: none !important;
}

.event-type-select:hover {
  border-color: #40a9ff !important;
}

.event-type-select:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.event-type-select option {
  padding: 8px 12px !important;
  color: rgba(0, 0, 0, 0.85) !important;
  background: white !important;
}

/* 设备状态弹窗样式 */
.device-status-container {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 12px;
}

.device-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.device-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.device-item.online {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.device-item.offline {
  background: #fff2f0;
  border-color: #ffccc7;
}

.device-item.long-offline {
  background: #fff7e6;
  border-color: #ffd591;
}

.device-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.device-item.online .device-icon {
  color: #52c41a;
}

.device-item.offline .device-icon {
  color: #ff4d4f;
}

.device-item.long-offline .device-icon {
  color: #fa8c16;
}

.device-info {
  text-align: center;
}

.device-number {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.device-status {
  font-size: 10px;
  color: #666;
}
</style>
