<template>
  <div class="info-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">
            <smile-outlined class="title-icon" />
            用户信息管理
          </h2>
        </div>
        <div class="header-right">
          <button @click="showAddUserModal" class="add-user-btn">
            <user-add-outlined />
            添加用户
          </button>
          <button
            v-if="selectedUsers.length > 0"
            @click="batchDeleteUsers"
            class="batch-delete-btn"
          >
            <delete-outlined />
            批量删除 ({{ selectedUsers.length }})
          </button>
          <button @click="goToFaceManagement" class="face-management-btn">
            <camera-outlined />
            人脸管理
          </button>
          <button @click="goBack" class="back-btn">
            <arrow-left-outlined />
            返回首页
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <div class="loading-spinner"></div>
      <p>加载用户信息中...</p>
    </div>

    <!-- 用户信息表格 -->
    <div class="table-container" v-else>
      <!-- 用户表格 -->
      <a-table
        :columns="columns"
        :data-source="users"
        :pagination="{ pageSize: 5}"
        :row-selection="rowSelection"
        class="user-table"
        size="small"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'username'">
            <span class="header-cell">
              <user-outlined />
              用户名
            </span>
          </template>
          <!-- <template v-else-if="column.key === 'avatar'">
            <span class="header-cell">
              <picture-outlined />
              头像
            </span>
          </template> -->
          <template v-else-if="column.key === 'tags'">
            <span class="header-cell">
              <tag-outlined />
              角色权限
            </span>
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'username'">
            <div class="username-cell">
              <strong>{{ record.username }}</strong>
            </div>
          </template>
          <!-- <template v-else-if="column.key === 'avatar'">
            <div class="avatar-cell">
              <img
                v-if="record.avatar"
                :src="getAvatarUrl(record.avatar)"
                :alt="record.username + '的头像'"
                class="user-avatar"
                @error="handleAvatarError"
              />
              <div v-else class="no-avatar">
                <user-outlined />
              </div>
            </div>
          </template> -->
          <template v-else-if="column.key === 'age'">
            <div class="age-cell">
              {{ record.age ? record.age + '岁' : '未知' }}
            </div>
          </template>
          <template v-else-if="column.key === 'tags'">
            <span class="tags-cell">
              <a-tag
                v-for="tag in record.tags"
                :key="tag"
                :color="getTagColor(tag)"
                class="role-tag"
              >
                {{ tag }}
              </a-tag>
            </span>
          </template>
          <template v-else-if="column.key === 'face_info'"  >
            <div class="face-cell" @click="manageFace(record)">
              <div v-if="record.face_info" class="face-preview">
                <img
                  :src="getFaceUrl(record.face_info)"
                  :alt="record.username + '的人脸'"
                  class="face-image"
                  @error="handleFaceError"
                />
              </div>
              <div v-else class="no-face">
                <user-outlined />
                <span>未设置</span>
              </div>
            </div>
          </template>
          <template v-else-if="column.key === 'action'">
            <div class="action-cell">
              <a-button size="small" type="primary" ghost @click="viewUser(record)">
                查看
              </a-button>
              <a-button size="small" type="default" @click="showEditUser(record)">
                <edit-outlined />
                编辑
              </a-button>
              <a-button size="small" type="link" danger @click="deleteUser(record)">
                <delete-outlined />
                删除
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 人脸管理模态框 -->
    <a-modal
      v-model:open="faceModalVisible"
      :title="`${selectedUser?.username} - 人脸管理`"
      width="700px"
      :footer="null"
      @cancel="closeFaceModal"
    >
      <FaceCapture
        v-if="selectedUser"
        :user-id="selectedUser.id"
        :current-face-info="selectedUser.face_info"
        @face-updated="handleFaceUpdated"
        @face-deleted="handleFaceDeleted"
      />
    </a-modal>

    <!-- 编辑用户模态框 -->
    <a-modal
      v-model:open="editModalVisible"
      :title="`编辑用户 - ${selectedUser?.username}`"
      width="600px"
      @ok="saveEditUser"
      @cancel="editModalVisible = false"
      :confirm-loading="saving"
    >
      <div class="edit-form">
        <div class="form-row">
          <div class="form-item">
            <label>用户名</label>
            <a-input v-model:value="editForm.username" placeholder="请输入用户名" />
          </div>
          <div class="form-item">
            <label>邮箱</label>
            <a-input v-model:value="editForm.email" placeholder="请输入邮箱" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>性别</label>
            <a-select v-model:value="editForm.sex" placeholder="请选择性别">
              <a-select-option value="">未设置</a-select-option>
              <a-select-option value="男">男</a-select-option>
              <a-select-option value="女">女</a-select-option>
            </a-select>
          </div>
          <div class="form-item">
            <label>出生日期</label>
            <a-input v-model:value="editForm.birth_date" type="date" placeholder="请选择出生日期" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>年级</label>
            <a-input v-model:value="editForm.grade" placeholder="请输入年级" />
          </div>
          <div class="form-item">
            <label>班级/部门</label>
            <a-input v-model:value="editForm.department" placeholder="请输入班级或部门" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>学号/教职号</label>
            <a-input v-model:value="editForm.number" placeholder="请输入学号或教职号" />
          </div>
          <div class="form-item">
            <label>联系方式</label>
            <a-input v-model:value="editForm.phone" placeholder="请输入联系方式" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>角色权限</label>
            <a-select v-model:value="editForm.role" placeholder="请选择角色">
              <a-select-option value="student">学生</a-select-option>
              <a-select-option value="teacher">教师</a-select-option>
              <a-select-option value="admin">管理员</a-select-option>
            </a-select>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 添加用户模态框 -->
    <a-modal
      v-model:open="addModalVisible"
      title="添加新用户"
      width="600px"
      @ok="saveAddUser"
      @cancel="addModalVisible = false"
      :confirm-loading="saving"
    >
      <div class="edit-form">
        <div class="form-row">
          <div class="form-item">
            <label>用户名 *</label>
            <a-input v-model:value="addForm.username" placeholder="请输入用户名" />
          </div>
          <div class="form-item">
            <label>邮箱 *</label>
            <a-input v-model:value="addForm.email" placeholder="请输入邮箱" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>密码 *</label>
            <a-input-password v-model:value="addForm.password" placeholder="请输入密码" />
          </div>
          <div class="form-item">
            <label>性别</label>
            <a-select v-model:value="addForm.sex" placeholder="请选择性别">
              <a-select-option value="">未设置</a-select-option>
              <a-select-option value="男">男</a-select-option>
              <a-select-option value="女">女</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>出生日期</label>
            <a-input v-model:value="addForm.birth_date" type="date" placeholder="请选择出生日期" />
          </div>
          <div class="form-item">
            <label>年级</label>
            <a-input v-model:value="addForm.grade" placeholder="请输入年级" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>班级/部门</label>
            <a-input v-model:value="addForm.department" placeholder="请输入班级或部门" />
          </div>
          <div class="form-item">
            <label>学号/教职号</label>
            <a-input v-model:value="addForm.number" placeholder="请输入学号或教职号" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>联系方式</label>
            <a-input v-model:value="addForm.phone" placeholder="请输入联系方式" />
          </div>
          <div class="form-item">
            <label>角色权限</label>
            <a-select v-model:value="addForm.role" placeholder="请选择角色">
              <a-select-option value="student">学生</a-select-option>
              <a-select-option value="teacher">教师</a-select-option>
              <a-select-option value="admin">管理员</a-select-option>
            </a-select>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  SmileOutlined,
  ArrowLeftOutlined,
  UserOutlined,
  PictureOutlined,
  TagOutlined,
  CameraOutlined,
  UserAddOutlined,
  DeleteOutlined,
  EditOutlined
} from '@ant-design/icons-vue';
import {
  Table as ATable,
  Tag as ATag,
  Button as AButton,
  Modal as AModal,
  Input as AInput,
  Select as ASelect,
  SelectOption as ASelectOption,
  InputPassword as AInputPassword,
  message
} from 'ant-design-vue';
import { authAPI } from '../utils/api';
import FaceCapture from './FaceCapture.vue';

const router = useRouter();
const users = ref([]);
const loading = ref(true);
const faceModalVisible = ref(false);
const selectedUser = ref(null);
const currentUser = ref(null);
const selectedUsers = ref([]);
const editModalVisible = ref(false);
const addModalVisible = ref(false);
const editForm = ref({
  username: '',
  email: '',
  sex: '',
  birth_date: '',
  grade: '',
  department: '',
  number: '',
  phone: '',
  role: 'student'
});
const addForm = ref({
  username: '',
  email: '',
  password: '',
  sex: '',
  birth_date: '',
  grade: '',
  department: '',
  number: '',
  phone: '',
  role: 'student'
});
const saving = ref(false);

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedUsers,
  onChange: (selectedRowKeys) => {
    selectedUsers.value = selectedRowKeys;
  },
  getCheckboxProps: (record) => ({
    disabled: record.id === currentUser.value?.id, // 不能选择自己
  }),
};

// 表格列配置
const columns = [
  // {
  //   title: '头像',
  //   key: 'avatar',
  //   dataIndex: 'avatar',
  //   width: 60,
  //   align: 'center',
  // },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 120,
    ellipsis: true,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    key: 'sex',
    width: 80,
    align: 'center',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    width: 80,
    align: 'center',
  },
  {
    title: '年级',
    dataIndex: 'grade',
    key: 'grade',
    width: 100,
    ellipsis: true,
  },
  {
    title: '班级/部门',
    dataIndex: 'department',
    key: 'department',
    width: 140,
    ellipsis: true,
  },
  {
    title: '学号/教职号',
    dataIndex: 'number',
    key: 'number',
    width: 140,
    ellipsis: true,
  },
  {
    title: '联系方式',
    dataIndex: 'phone',
    key: 'phone',
    width: 140,
    ellipsis: true,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 180,
    ellipsis: true,
  },
  {
    title: '角色',
    key: 'tags',
    dataIndex: 'tags',
    width: 100,
    align: 'center',
  },
  {
    title: '人脸',
    key: 'face_info',
    dataIndex: 'face_info',
    width: 80,
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
];

// 获取当前用户信息
const fetchCurrentUser = async () => {
  try {
    const response = await authAPI.getUserProfile();
    currentUser.value = response.data.user;
    console.log('当前登录用户:', currentUser.value);
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    // 如果API失败，尝试从本地存储获取
    const localUser = authUtils.getCurrentUser();
    if (localUser) {
      currentUser.value = localUser;
      console.log('从本地存储获取当前用户:', currentUser.value);
    }
  }
};

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true;
  try {
    const response = await authAPI.getUsersList();
    users.value = response.data.users.map(user => ({
      ...user,
      key: user.id.toString()
    }));
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取头像URL
const getAvatarUrl = (filename) => {
  return authAPI.getAvatarUrl(filename);
};

// 处理头像加载错误
const handleAvatarError = (event) => {
  event.target.style.display = 'none';
  event.target.nextElementSibling.style.display = 'flex';
};

// 获取人脸图片URL
const getFaceUrl = (filename) => {
  return authAPI.getFaceUrl(filename);
};

// 处理人脸图片加载错误
const handleFaceError = (event) => {
  event.target.style.display = 'none';
  const parent = event.target.parentElement;
  if (parent) {
    parent.innerHTML = '<div class="no-face"><span>加载失败</span></div>';
  }
};

// 获取标签颜色
const getTagColor = (tag) => {
  const colorMap = {
    '管理员': 'red',
    '教师': 'blue',
    '学生': 'green'
  };
  return colorMap[tag] || 'default';
};

// 查看用户详情
const viewUser = (user) => {
  console.log('查看用户:', user);
  router.push(`/profile/${user.id}`);
};

// 显示编辑用户模态框
const showEditUser = (user) => {
  selectedUser.value = user;
  editForm.value = {
    username: user.username,
    email: user.email,
    sex: user.sex || '',
    birth_date: user.birth_date || '',
    grade: user.grade || '',
    department: user.department || '',
    number: user.number || '',
    phone: user.phone || '',
    role: user.role
  };
  editModalVisible.value = true;
};

// 保存编辑的用户信息
const saveEditUser = async () => {
  try {
    saving.value = true;
    await authAPI.adminUpdateUser(selectedUser.value.id, editForm.value);
    message.success('用户信息更新成功');
    editModalVisible.value = false;
    fetchUsers(); // 重新获取用户列表
  } catch (error) {
    console.error('更新用户信息失败:', error);
    message.error(error.response?.data?.error || '更新失败');
  } finally {
    saving.value = false;
  }
};

// 显示添加用户模态框
const showAddUserModal = () => {
  addForm.value = {
    username: '',
    email: '',
    password: '',
    sex: '',
    grade: '',
    number: '',
    phone: '',
    role: 'student'
  };
  addModalVisible.value = true;
};

// 保存新用户
const saveAddUser = async () => {
  try {
    saving.value = true;
    await authAPI.adminCreateUser(addForm.value);
    message.success('用户创建成功');
    addModalVisible.value = false;
    fetchUsers(); // 重新获取用户列表
  } catch (error) {
    console.error('创建用户失败:', error);
    message.error(error.response?.data?.error || '创建失败');
  } finally {
    saving.value = false;
  }
};

// 删除单个用户
const deleteUser = async (user) => {
  if (user.id === currentUser.value?.id) {
    message.error('不能删除自己的账户');
    return;
  }

  try {
    await authAPI.adminDeleteUser(user.id);
    message.success('用户删除成功');
    fetchUsers(); // 重新获取用户列表
  } catch (error) {
    console.error('删除用户失败:', error);
    message.error(error.response?.data?.error || '删除失败');
  }
};

// 批量删除用户
const batchDeleteUsers = async () => {
  if (selectedUsers.value.length === 0) {
    message.warning('请选择要删除的用户');
    return;
  }

  try {
    await authAPI.adminBatchDeleteUsers(selectedUsers.value);
    message.success(`成功删除 ${selectedUsers.value.length} 个用户`);
    selectedUsers.value = [];
    fetchUsers(); // 重新获取用户列表
  } catch (error) {
    console.error('批量删除用户失败:', error);
    message.error(error.response?.data?.error || '删除失败');
  }
};

// 切换用户密码显示状态
const toggleUserPassword = async (user) => {
  if (!user.showPassword) {
    // 显示密码时，获取解密后的密码
    await fetchUserDecryptedPassword(user);
  }
  user.showPassword = !user.showPassword;
};

// 获取用户解密后的密码（这里使用默认密码演示）
const fetchUserDecryptedPassword = async (user) => {
  try {
    // 由于这是管理员查看其他用户的密码，我们使用默认密码进行演示
    // 在实际应用中，这应该通过专门的管理员API来实现
    user.decryptedPassword = 'password123'; // 演示用的默认密码
  } catch (error) {
    console.error('获取解密密码失败:', error);
    user.decryptedPassword = '获取失败';
  }
};

// // 管理自己的人脸
// const manageMyFace = () => {
//   if (!currentUser.value) {
//     message.error('无法获取当前用户信息');
//     return;
//   }

//   // 创建一个包含当前用户信息的对象，用于人脸管理
//   selectedUser.value = {
//     id: 'current', // 特殊标识，表示当前用户
//     username: currentUser.value.username,
//     face_info: currentUser.value.face_info || null
//   };
//   faceModalVisible.value = true;
//   console.log('打开当前用户人脸管理:', selectedUser.value);
// };

// 人脸管理
const manageFace = (user) => {
  selectedUser.value = user;
  faceModalVisible.value = true;
};

// 关闭人脸管理模态框
const closeFaceModal = () => {
  faceModalVisible.value = false;
  selectedUser.value = null;
};

// 处理人脸更新
const handleFaceUpdated = (faceInfo) => {
  if (selectedUser.value) {
    console.log(`用户 ${selectedUser.value.username} (ID: ${selectedUser.value.id}) 的人脸信息已更新:`, faceInfo);

    if (selectedUser.value.id === 'current') {
      // 更新当前用户的人脸信息
      if (currentUser.value) {
        currentUser.value.face_info = faceInfo;
        console.log('当前用户人脸信息已更新:', faceInfo);
      }
      selectedUser.value.face_info = faceInfo;
    } else {
      // 更新用户列表中的人脸信息
      const userIndex = users.value.findIndex(u => u.id === selectedUser.value.id);
      if (userIndex !== -1) {
        const oldFaceInfo = users.value[userIndex].face_info;
        users.value[userIndex].face_info = faceInfo;
        selectedUser.value.face_info = faceInfo;
        console.log(`用户 ${selectedUser.value.username} 人脸信息从 ${oldFaceInfo} 更新为 ${faceInfo}`);
      }
    }
  }
  message.success('人脸信息更新成功');

  // 重新获取用户列表以确保数据同步
  fetchUsers();
};

// 处理人脸删除
const handleFaceDeleted = () => {
  if (selectedUser.value) {
    if (selectedUser.value.id === 'current') {
      // 删除当前用户的人脸信息
      if (currentUser.value) {
        currentUser.value.face_info = null;
        console.log('当前用户人脸信息已删除');
      }
      selectedUser.value.face_info = null;
    } else {
      // 更新用户列表中的人脸信息
      const userIndex = users.value.findIndex(u => u.id === selectedUser.value.id);
      if (userIndex !== -1) {
        users.value[userIndex].face_info = null;
        selectedUser.value.face_info = null;
      }
    }
  }
  message.success('人脸信息删除成功');

  // 重新获取用户列表以确保数据同步
  fetchUsers();
};

// 跳转到人脸管理页面
const goToFaceManagement = () => {
  router.push('/face-management');
};

// 返回首页
const goBack = () => {
  router.push('/home');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCurrentUser(); // 获取当前用户信息
  fetchUsers();
});
</script>

<style scoped>
.info-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 28px;
}


.header-right {
  display: flex;
  gap: 12px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
  font-size: px;
}

.back-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f0f8ff;
}

.face-management-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #1890ff;
  border-radius: 6px;
  background: #1890ff;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 15px;
}

.face-management-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #666;
  font-size: 20px;
  margin: 0;
}

/* 表格容器样式 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 表格样式 */
.user-table {
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-table :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e8e8e8;
  padding: 12px 8px;
  font-size: 18px;
  white-space: nowrap;
}

.user-table :deep(.ant-table-tbody > tr > td) {
  padding: 12px 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 18px;
  line-height: 1.5;
  vertical-align: middle;
}

.user-table :deep(.ant-table-tbody > tr:hover > td) {
  background: #f0f8ff;
}

.user-table :deep(.ant-table-small) .ant-table-thead > tr > th,
.user-table :deep(.ant-table-small) .ant-table-tbody > tr > td {
  padding: 6px 4px;
}

/* 表格内容样式优化 */
.user-table :deep(.ant-table-cell-ellipsis) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 0;
}

/* 表格单元格样式 */
.header-cell {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
}

.username-cell {
  display: flex;
  align-items: center;
}

.username-cell strong {
  color: #1890ff;
  font-size: 18px;
}

.avatar-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8e8e8;
}

/* .my-face-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #52c41a;
  border-radius: 6px;
  background: #52c41a;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.my-face-btn:hover {
  background: #73d13d;
  border-color: #73d13d;
} */
.no-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 16px;
  border: 2px solid #e8e8e8;
}

.tags-cell {
  display: flex;
  gap: 2px;
  flex-wrap: wrap;
  justify-content: center;
}

.role-tag {
  font-size: 16px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  line-height: 1.2;
}

.action-cell {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.action-cell .ant-btn {
  font-size: 16px;
  height: 32px;
  padding: 4px 12px;
  line-height: 1.2;
}

/* 人脸信息单元格样式 */
.face-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.face-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.face-image {
  width: 75px;
  height: 75px;
  border-radius: 6px;
  object-fit: cover;
  border: 2px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.3s;
}

.face-image:hover {
  border-color: #1890ff;
  transform: scale(1.05);
}

.no-face {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 16px;
}

.no-face .anticon {
  font-size: 20px;
}

/* 年龄列样式 */
.age-cell {
  text-align: center;
  font-weight: 500;
  color: #666;
  font-size: 18px;
}

/* 按钮样式 */
.add-user-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #52c41a;
  border-radius: 6px;
  background: #52c41a;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.add-user-btn:hover {
  background: #73d13d;
  border-color: #73d13d;
}

.batch-delete-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #ff4d4f;
  border-radius: 6px;
  background: #ff4d4f;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.batch-delete-btn:hover {
  background: #ff7875;
  border-color: #ff7875;
}

/* 表单样式 */
.edit-form {
  padding: 20px 0;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-item .ant-input,
.form-item .ant-select {
  height: 36px;
}

.form-item .ant-select-selector {
  height: 36px !important;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-container {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .page-title {
    font-size: 20px;
  }

  .user-table :deep(.ant-table) {
    font-size: 12px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .no-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
</style>