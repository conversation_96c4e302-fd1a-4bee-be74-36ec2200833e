<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { authAPI, authUtils } from '../src/utils/api';

const router = useRouter();
const route = useRoute();
const user = ref(null);

// 更新用户信息
const updateUserInfo = () => {
  if (authUtils.isAuthenticated()) {
    user.value = authUtils.getCurrentUser();
  } else {
    user.value = null;
  }
};

// 检查登录状态
const checkAuth = () => {
  if (!authUtils.isAuthenticated()) {
    user.value = null;
    router.push('/');
    return false;
  }

  updateUserInfo();
  return true;
};

// 验证token有效性
const verifyToken = async () => {
  try {
    await authAPI.verifyToken();
    // Token验证成功后更新用户信息
    updateUserInfo();
  } catch (error) {
    console.error('Token验证失败:', error);
    logout();
  }
};

// 获取用户头像URL
const getUserAvatarUrl = () => {
  if (user.value && user.value.avatar) {
    return authAPI.getAvatarUrl(user.value.avatar);
  }
  // 返回默认头像
  return new URL('../src/assets/avatar/default-avatar.svg', import.meta.url).href;
};

// 头像加载错误处理
const handleAvatarError = (event) => {
  // 如果头像加载失败，使用默认头像
  event.target.src = new URL('../src/assets/avatar/default-avatar.svg', import.meta.url).href;
};

// 跳转到个人设置页面
const goToProfile = () => {
  router.push('/profile');
};
// 跳转到info页面
const goToInfo = () => {
  router.push('/info');
};
// 退出登录
const logout = () => {
  authUtils.clearAuth();
  user.value = null; // 立即清除用户信息

  // 触发自定义事件，通知状态变化
  window.dispatchEvent(new Event('authStateChanged'));

  router.push('/');
};

// 监听路由变化，更新用户状态
watch(() => route.path, () => {
  updateUserInfo();
}, { immediate: true });

// 监听localStorage变化，实时更新用户信息
const handleStorageChange = () => {
  updateUserInfo();
};

onMounted(() => {
  updateUserInfo(); // 初始化时更新用户信息

  // 监听localStorage变化
  window.addEventListener('storage', handleStorageChange);

  // 监听自定义事件（用于同一页面内的状态更新）
  window.addEventListener('authStateChanged', handleStorageChange);

  if (checkAuth()) {
    verifyToken();
  }
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('storage', handleStorageChange);
  window.removeEventListener('authStateChanged', handleStorageChange);
});
</script>

<template>
  <div id="app">
        <div class="header">
      <h1>基于人工智能的实训室智慧管理系统</h1>
      <div class="user-info" v-if="user">
        <div class="user-avatar" @click="goToProfile" title="点击设置头像">
          <img
            :src="getUserAvatarUrl()"
            :alt="user.username + '的头像'"
            class="avatar-img"
            @error="handleAvatarError"
          />
        </div>
        <span class="username">{{ user.username }}</span>
        <button @click="goToInfo" class="logout-btn">个人信息</button>
        <button @click="logout" class="logout-btn">退出登录</button>
      </div>
    </div>
    <router-view />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主应用容器 - 上下结构 */
#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 头部样式 */
.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0; /* 防止头部被压缩 */
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8)) border-box;
  transition: all 0.4s ease;
  cursor: pointer;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-avatar:hover::before {
  opacity: 1;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: all 0.3s ease;
}

.user-avatar:hover .avatar-img {
  transform: scale(1.05);
}

.username {
  font-size: 1rem;
  opacity: 0.95;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.username:hover {
  opacity: 1;
  transform: translateY(-1px);
}

/* 按钮样式 */
.logout-btn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s ease;
  font-size: 0.9rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}
.logout-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 路由视图容器 - 占据剩余空间 */
.router-view {
  flex: 1;
  width: 100%;
  overflow: auto; /* 允许内容滚动 */
}
</style>
