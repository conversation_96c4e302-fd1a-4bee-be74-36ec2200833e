<template>
  <div class="attendance-statistics">
    <div class="header">
      <h2>签到统计</h2>
    </div>

    <!-- 筛选条件 -->
    <div class="filters">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select
            v-model:value="filters.department"
            placeholder="选择班级/部门"
            allowClear
            style="width: 100%"
            @change="loadStatistics"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="计算机科学与技术1班">计算机科学与技术1班</a-select-option>
            <a-select-option value="软件工程2班">软件工程2班</a-select-option>
            <a-select-option value="信息安全1班">信息安全1班</a-select-option>
            <a-select-option value="网络工程1班">网络工程1班</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-date-picker
            v-model:value="filters.start_date"
            placeholder="开始日期"
            style="width: 100%"
            @change="loadStatistics"
          />
        </a-col>
        <a-col :span="6">
          <a-date-picker
            v-model:value="filters.end_date"
            placeholder="结束日期"
            style="width: 100%"
            @change="loadStatistics"
          />
        </a-col>
        <a-col :span="6">
          <a-button @click="resetFilters">重置筛选</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6" v-for="stat in statusStatistics" :key="stat.status">
          <a-card>
            <a-statistic
              :title="getStatusText(stat.status)"
              :value="stat.count"
              :value-style="{ color: getStatusColor(stat.status) }"
            >
              <template #suffix>
                <span style="font-size: 12px; color: #666;">
                  (调整: {{ stat.adjusted_count }})
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 标签页 -->
    <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
      <a-tab-pane key="student" tab="按学生统计">
        <a-table
          :columns="studentColumns"
          :data-source="studentStatistics"
          :loading="loading"
          :pagination="{ pageSize: 20 }"
          row-key="student_id"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'attendance_rate'">
              <a-progress
                :percent="getAttendanceRate(record)"
                :status="getAttendanceRate(record) >= 80 ? 'success' : 'exception'"
                size="small"
              />
            </template>
            <template v-if="column.key === 'action'">
              <a-button type="link" size="small" @click="viewStudentDetail(record)">
                查看详情
              </a-button>
            </template>
          </template>
        </a-table>
      </a-tab-pane>

      <a-tab-pane key="course" tab="按课程统计">
        <a-table
          :columns="courseColumns"
          :data-source="courseStatistics"
          :loading="loading"
          :pagination="{ pageSize: 20 }"
          row-key="course_id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'attendance_rate'">
              <a-progress
                :percent="getCourseAttendanceRate(record)"
                :status="getCourseAttendanceRate(record) >= 80 ? 'success' : 'exception'"
                size="small"
              />
            </template>
          </template>
        </a-table>
      </a-tab-pane>

      <a-tab-pane key="records" tab="详细记录">
        <div class="record-filters">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-select
                v-model:value="recordFilters.status"
                placeholder="选择状态"
                allowClear
                style="width: 100%"
                @change="loadRecords"
              >
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="present">正常</a-select-option>
                <a-select-option value="late">迟到</a-select-option>
                <a-select-option value="absent">缺勤</a-select-option>
                <a-select-option value="truant">旷课</a-select-option>
                <a-select-option value="early_leave">早退</a-select-option>
                <a-select-option value="sick_leave">病假</a-select-option>
                <a-select-option value="personal_leave">事假</a-select-option>
                <a-select-option value="official_leave">公假</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-input
                v-model:value="recordFilters.student_number"
                placeholder="输入学号搜索"
                @pressEnter="loadRecords"
              />
            </a-col>
            <a-col :span="6">
              <a-button @click="loadRecords">搜索</a-button>
            </a-col>
          </a-row>
        </div>

        <a-table
          :columns="recordColumns"
          :data-source="attendanceRecords"
          :loading="loadingRecords"
          :pagination="{ pageSize: 50 }"
          row-key="id"
          :scroll="{ x: 1400 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
              <div v-if="record.adjusted_at" style="font-size: 12px; color: #666;">
                (已调整)
              </div>
            </template>
            <template v-if="column.key === 'check_in_time'">
              {{ record.check_in_time ? formatDateTime(record.check_in_time) : '-' }}
            </template>
            <template v-if="column.key === 'check_out_time'">
              {{ record.check_out_time ? formatDateTime(record.check_out_time) : '-' }}
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>

    <!-- 学生详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="学生签到详情"
      :footer="null"
      width="800px"
    >
      <div v-if="selectedStudent">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="学生姓名">
            {{ selectedStudent.student_name }}
          </a-descriptions-item>
          <a-descriptions-item label="学号">
            {{ selectedStudent.student_number }}
          </a-descriptions-item>
          <a-descriptions-item label="班级/部门">
            {{ selectedStudent.department }}
          </a-descriptions-item>
          <a-descriptions-item label="出勤率">
            {{ getAttendanceRate(selectedStudent) }}%
          </a-descriptions-item>
        </a-descriptions>

        <div style="margin-top: 16px;">
          <h4>出勤统计</h4>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总记录" :value="selectedStudent.total_records" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="正常" :value="selectedStudent.present_count" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="迟到" :value="selectedStudent.late_count" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="缺勤" :value="selectedStudent.absent_count" />
            </a-col>
          </a-row>
          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="6">
              <a-statistic title="旷课" :value="selectedStudent.truant_count" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="早退" :value="selectedStudent.early_leave_count" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="病假" :value="selectedStudent.sick_leave_count" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="事假" :value="selectedStudent.personal_leave_count" />
            </a-col>
          </a-row>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

export default {
  name: 'AttendanceStatistics',
  setup() {
    const loading = ref(false)
    const loadingRecords = ref(false)
    const activeTab = ref('student')
    const detailModalVisible = ref(false)
    
    const statusStatistics = ref([])
    const studentStatistics = ref([])
    const courseStatistics = ref([])
    const attendanceRecords = ref([])
    const selectedStudent = ref(null)

    const filters = reactive({
      department: '',
      start_date: null,
      end_date: null
    })

    const recordFilters = reactive({
      status: '',
      student_number: ''
    })

    const studentColumns = [
      { title: '学生姓名', dataIndex: 'student_name', key: 'student_name', width: 100 },
      { title: '学号', dataIndex: 'student_number', key: 'student_number', width: 120 },
      { title: '班级/部门', dataIndex: 'department', key: 'department', width: 150 },
      { title: '总记录', dataIndex: 'total_records', key: 'total_records', width: 80 },
      { title: '正常', dataIndex: 'present_count', key: 'present_count', width: 60 },
      { title: '迟到', dataIndex: 'late_count', key: 'late_count', width: 60 },
      { title: '缺勤', dataIndex: 'absent_count', key: 'absent_count', width: 60 },
      { title: '旷课', dataIndex: 'truant_count', key: 'truant_count', width: 60 },
      { title: '早退', dataIndex: 'early_leave_count', key: 'early_leave_count', width: 60 },
      { title: '病假', dataIndex: 'sick_leave_count', key: 'sick_leave_count', width: 60 },
      { title: '事假', dataIndex: 'personal_leave_count', key: 'personal_leave_count', width: 60 },
      { title: '公假', dataIndex: 'official_leave_count', key: 'official_leave_count', width: 60 },
      { title: '调整次数', dataIndex: 'adjusted_count', key: 'adjusted_count', width: 80 },
      { title: '出勤率', key: 'attendance_rate', width: 120 },
      { title: '操作', key: 'action', width: 100, fixed: 'right' }
    ]

    const courseColumns = [
      { title: '课程名称', dataIndex: 'course_name', key: 'course_name', width: 150 },
      { title: '班级/部门', dataIndex: 'department', key: 'department', width: 150 },
      { title: '总记录', dataIndex: 'total_records', key: 'total_records', width: 80 },
      { title: '正常', dataIndex: 'present_count', key: 'present_count', width: 60 },
      { title: '迟到', dataIndex: 'late_count', key: 'late_count', width: 60 },
      { title: '缺勤', dataIndex: 'absent_count', key: 'absent_count', width: 60 },
      { title: '旷课', dataIndex: 'truant_count', key: 'truant_count', width: 60 },
      { title: '出勤率', key: 'attendance_rate', width: 120 }
    ]

    const recordColumns = [
      { title: '学生姓名', dataIndex: 'student_name', key: 'student_name', width: 100 },
      { title: '学号', dataIndex: 'student_number', key: 'student_number', width: 120 },
      { title: '班级/部门', dataIndex: 'department', key: 'department', width: 120 },
      { title: '课程名称', dataIndex: 'course_name', key: 'course_name', width: 150 },
      { title: '上课地点', dataIndex: 'classroom', key: 'classroom', width: 100 },
      { title: '签到日期', dataIndex: 'attendance_date', key: 'attendance_date', width: 100 },
      { title: '签到时间', key: 'check_in_time', width: 150 },
      { title: '签退时间', key: 'check_out_time', width: 150 },
      { title: '状态', key: 'status', width: 100 },
      { title: '调整人', dataIndex: 'adjuster_name', key: 'adjuster_name', width: 100 }
    ]

    const getStatusColor = (status) => {
      const colors = {
        present: '#52c41a',
        late: '#faad14',
        absent: '#f5222d',
        truant: '#f5222d',
        early_leave: '#faad14',
        sick_leave: '#1890ff',
        personal_leave: '#1890ff',
        official_leave: '#722ed1'
      }
      return colors[status] || '#d9d9d9'
    }

    const getStatusText = (status) => {
      const texts = {
        present: '正常',
        late: '迟到',
        absent: '缺勤',
        truant: '旷课',
        early_leave: '早退',
        sick_leave: '病假',
        personal_leave: '事假',
        official_leave: '公假'
      }
      return texts[status] || status
    }

    const getAttendanceRate = (record) => {
      if (record.total_records === 0) return 0
      const attendedCount = record.present_count + record.late_count + record.sick_leave_count + 
                           record.personal_leave_count + record.official_leave_count
      return Math.round((attendedCount / record.total_records) * 100)
    }

    const getCourseAttendanceRate = (record) => {
      if (record.total_records === 0) return 0
      const attendedCount = record.present_count + record.late_count
      return Math.round((attendedCount / record.total_records) * 100)
    }

    const formatDateTime = (dateTimeStr) => {
      return dayjs(dateTimeStr).format('MM-DD HH:mm')
    }

    // 加载统计数据
    const loadStatistics = async () => {
      loading.value = true
      try {
        const params = new URLSearchParams()
        if (filters.department) params.append('department', filters.department)
        if (filters.start_date) params.append('start_date', filters.start_date.format('YYYY-MM-DD'))
        if (filters.end_date) params.append('end_date', filters.end_date.format('YYYY-MM-DD'))

        const response = await fetch(`/api/attendance/statistics?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          statusStatistics.value = data.status_statistics || []
          studentStatistics.value = data.student_statistics || []
          courseStatistics.value = data.course_statistics || []
        } else {
          const error = await response.json()
          message.error(error.error || '加载统计数据失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        loading.value = false
      }
    }

    // 加载详细记录
    const loadRecords = async () => {
      loadingRecords.value = true
      try {
        const params = new URLSearchParams()
        if (filters.department) params.append('department', filters.department)
        if (filters.start_date) params.append('start_date', filters.start_date.format('YYYY-MM-DD'))
        if (filters.end_date) params.append('end_date', filters.end_date.format('YYYY-MM-DD'))
        if (recordFilters.status) params.append('status', recordFilters.status)
        if (recordFilters.student_number) {
          // 根据学号查找学生ID
          const studentResponse = await fetch('/api/users', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            }
          })
          if (studentResponse.ok) {
            const studentData = await studentResponse.json()
            const student = studentData.users.find(u => u.number === recordFilters.student_number)
            if (student) {
              params.append('student_id', student.id)
            }
          }
        }

        const response = await fetch(`/api/attendance/records?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          attendanceRecords.value = data.records || []
        } else {
          const error = await response.json()
          message.error(error.error || '加载记录失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        loadingRecords.value = false
      }
    }

    const handleTabChange = (key) => {
      activeTab.value = key
      if (key === 'records') {
        loadRecords()
      }
    }

    const viewStudentDetail = (student) => {
      selectedStudent.value = student
      detailModalVisible.value = true
    }

    const resetFilters = () => {
      filters.department = ''
      filters.start_date = null
      filters.end_date = null
      loadStatistics()
    }

    onMounted(() => {
      loadStatistics()
    })

    return {
      loading,
      loadingRecords,
      activeTab,
      detailModalVisible,
      statusStatistics,
      studentStatistics,
      courseStatistics,
      attendanceRecords,
      selectedStudent,
      filters,
      recordFilters,
      studentColumns,
      courseColumns,
      recordColumns,
      getStatusColor,
      getStatusText,
      getAttendanceRate,
      getCourseAttendanceRate,
      formatDateTime,
      loadStatistics,
      loadRecords,
      handleTabChange,
      viewStudentDetail,
      resetFilters
    }
  }
}
</script>

<style scoped>
.attendance-statistics {
  padding: 24px;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 50%, #fff2e8 100%);
  min-height: calc(100vh - 80px);
}

.header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.header h2 {
  color: #1890ff;
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.filters {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.statistics-cards {
  margin-bottom: 32px;
}

.record-filters {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 统计卡片样式优化 */
:deep(.ant-card) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

:deep(.ant-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 700;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background: white;
}

:deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e8e8e8;
  font-size: 15px;
}

:deep(.ant-table-tbody > tr > td) {
  font-size: 14px;
  padding: 16px 12px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
}

/* 标签页样式优化 */
:deep(.ant-tabs) {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

:deep(.ant-tabs-tab) {
  font-weight: 500;
  font-size: 15px;
}

:deep(.ant-tabs-tab-active) {
  color: #1890ff;
}

:deep(.ant-tabs-ink-bar) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  height: 3px;
}

/* 按钮样式优化 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 表单样式优化 */
:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker),
:deep(.ant-range-picker) {
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
  font-size: 15px;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker-focused),
:deep(.ant-range-picker-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 8px;
  font-weight: 500;
  padding: 6px 12px;
  font-size: 13px;
  border: none;
}

/* 模态框样式优化 */
:deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

:deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

:deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

:deep(.ant-modal-close) {
  color: white;
}

:deep(.ant-modal-body) {
  padding: 24px;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-statistics {
    padding: 16px;
  }

  .header {
    padding: 20px;
    margin-bottom: 24px;
  }

  .header h2 {
    font-size: 24px;
  }

  .filters {
    padding: 20px;
    margin-bottom: 24px;
  }

  .record-filters {
    padding: 16px;
  }

  .statistics-cards {
    margin-bottom: 24px;
  }

  :deep(.ant-tabs) {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .attendance-statistics {
    padding: 12px;
  }

  .header {
    padding: 16px;
  }

  .header h2 {
    font-size: 20px;
  }

  .filters {
    padding: 16px;
  }

  .record-filters {
    padding: 12px;
  }

  :deep(.ant-tabs) {
    padding: 16px;
  }
}
</style>
