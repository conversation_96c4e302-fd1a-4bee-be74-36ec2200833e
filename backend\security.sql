/*
 Navicat Premium Data Transfer

 Source Server         : localhost3306
 Source Server Type    : MySQL
 Source Server Version : 80020
 Source Host           : localhost:3306
 Source Schema         : security

 Target Server Type    : MySQL
 Target Server Version : 80020
 File Encoding         : 65001

 Date: 23/07/2025 18:11:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for attendance_record
-- ----------------------------
DROP TABLE IF EXISTS `attendance_record`;
CREATE TABLE `attendance_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `course_id` int NOT NULL COMMENT '课程ID',
  `student_id` int NOT NULL COMMENT '学生ID',
  `attendance_date` date NOT NULL COMMENT '签到日期',
  `check_in_time` datetime NULL DEFAULT NULL COMMENT '签到时间',
  `check_out_time` datetime NULL DEFAULT NULL COMMENT '签退时间',
  `status` enum('present','late','absent','truant','early_leave','sick_leave','personal_leave','official_leave') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'absent' COMMENT '出勤状态',
  `original_status` enum('present','late','absent','truant','early_leave','sick_leave','personal_leave','official_leave') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原始状态（调整前）',
  `adjusted_by` int NULL DEFAULT NULL COMMENT '调整人ID',
  `adjusted_at` timestamp NULL DEFAULT NULL COMMENT '调整时间',
  `adjustment_reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '调整原因',
  `face_recognition_result` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '人脸识别结果',
  `ip_address` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `device_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '设备信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_course_student_date`(`course_id` ASC, `student_id` ASC, `attendance_date` ASC) USING BTREE,
  INDEX `idx_student_id`(`student_id` ASC) USING BTREE,
  INDEX `idx_attendance_date`(`attendance_date` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_adjusted_by`(`adjusted_by` ASC) USING BTREE,
  CONSTRAINT `attendance_record_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `attendance_record_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `attendance_record_ibfk_3` FOREIGN KEY (`adjusted_by`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of attendance_record
-- ----------------------------

-- ----------------------------
-- Table structure for check_out_session
-- ----------------------------
DROP TABLE IF EXISTS `check_out_session`;
CREATE TABLE `check_out_session`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `course_id` int NOT NULL COMMENT '课程ID',
  `teacher_id` int NOT NULL COMMENT '发起教师ID',
  `session_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '签退码',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_session_code`(`session_code` ASC) USING BTREE,
  INDEX `idx_course_id`(`course_id` ASC) USING BTREE,
  INDEX `idx_teacher_id`(`teacher_id` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE,
  CONSTRAINT `check_out_session_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `check_out_session_ibfk_2` FOREIGN KEY (`teacher_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of check_out_session
-- ----------------------------

-- ----------------------------
-- Table structure for course
-- ----------------------------
DROP TABLE IF EXISTS `course`;
CREATE TABLE `course`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `course_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程名称',
  `course_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '课程代码',
  `teacher_id` int NULL DEFAULT NULL COMMENT '授课教师ID',
  `classroom` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上课地点',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `day_of_week` tinyint NOT NULL COMMENT '星期几(1-7)',
  `weeks` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上课周次',
  `department` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '班级/部门',
  `semester` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '学期',
  `academic_year` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '学年',
  `credits` decimal(3, 1) NULL DEFAULT NULL COMMENT '学分',
  `course_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '课程类型',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '课程描述',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_teacher_id`(`teacher_id` ASC) USING BTREE,
  INDEX `idx_department`(`department` ASC) USING BTREE,
  INDEX `idx_day_of_week`(`day_of_week` ASC) USING BTREE,
  INDEX `idx_semester`(`semester` ASC) USING BTREE,
  CONSTRAINT `course_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of course
-- ----------------------------
-- 周一课程
INSERT INTO `course` VALUES (1, '数据结构与算法', 'CS101', 5, '实训室A101', '08:00:00', '09:40:00', 1, '1-16', '计算机科学与技术1班', '2024-2025-1', '2024-2025', 3.0, '专业必修', '学习基本的数据结构和算法设计', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (2, '操作系统原理', 'CS106', 6, '实训室B201', '10:00:00', '11:40:00', 1, '1-16', '软件工程2班', '2024-2025-1', '2024-2025', 3.0, '专业必修', '学习操作系统的基本原理和实现', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (3, 'Java程序设计', 'CS107', 5, '实训室C301', '14:00:00', '15:40:00', 1, '1-16', '信息安全1班', '2024-2025-1', '2024-2025', 2.5, '专业必修', '学习Java面向对象编程', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (4, '计算机网络', 'CS108', 6, '实训室A102', '16:00:00', '17:40:00', 1, '1-16', '网络工程1班', '2024-2025-1', '2024-2025', 3.0, '专业必修', '学习计算机网络协议和技术', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (5, '体育课', 'PE101', 7, '体育馆', '19:00:00', '20:40:00', 1, '1-16', '计算机科学与技术1班', '2024-2025-1', '2024-2025', 1.0, '体育课程', '体育锻炼课程', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');

-- 周二课程
INSERT INTO `course` VALUES (6, 'Web开发技术', 'CS102', 5, '实训室B201', '08:00:00', '09:40:00', 2, '1-16', '软件工程2班', '2024-2025-1', '2024-2025', 2.5, '专业必修', '学习前端和后端Web开发技术', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (7, '数据库系统', 'CS104', 5, '实训室A101', '10:00:00', '11:40:00', 2, '1-16', '计算机科学与技术1班', '2024-2025-1', '2024-2025', 3.0, '专业必修', '学习数据库设计和管理', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (8, 'Python程序设计', 'CS109', 6, '实训室C301', '14:00:00', '15:40:00', 2, '1-16', '信息安全1班', '2024-2025-1', '2024-2025', 2.0, '专业选修', '学习Python编程语言和应用', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (9, '软件测试', 'CS110', 5, '实训室B202', '16:00:00', '17:40:00', 2, '1-16', '软件工程2班', '2024-2025-1', '2024-2025', 2.0, '专业选修', '学习软件测试方法和工具', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (10, '体育课', 'PE102', 7, '体育馆', '19:00:00', '20:40:00', 2, '1-16', '软件工程2班', '2024-2025-1', '2024-2025', 1.0, '体育课程', '体育锻炼课程', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');

-- 周三课程
INSERT INTO `course` VALUES (11, '网络安全基础', 'CS103', 6, '实训室C301', '08:00:00', '09:40:00', 3, '1-16', '信息安全1班', '2024-2025-1', '2024-2025', 3.0, '专业必修', '学习网络安全的基本概念和技术', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (12, '算法分析与设计', 'CS111', 5, '实训室A101', '10:00:00', '11:40:00', 3, '1-16', '计算机科学与技术1班', '2024-2025-1', '2024-2025', 3.0, '专业必修', '学习高级算法设计和分析技术', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (13, '移动应用开发', 'CS112', 6, '实训室B201', '14:00:00', '15:40:00', 3, '1-16', '软件工程2班', '2024-2025-1', '2024-2025', 2.5, '专业选修', '学习Android和iOS应用开发', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (14, '网络协议分析', 'CS113', 5, '实训室C302', '16:00:00', '17:40:00', 3, '1-16', '网络工程1班', '2024-2025-1', '2024-2025', 2.5, '专业必修', '学习网络协议的深入分析', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (15, '数据库实验', 'LAB101', 5, '实验室A', '19:00:00', '20:40:00', 3, '1-16', '计算机科学与技术1班', '2024-2025-1', '2024-2025', 1.0, '实验课程', '数据库系统实验课程', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');

-- 周四课程
INSERT INTO `course` VALUES (16, '软件工程', 'CS105', 6, '实训室B201', '08:00:00', '09:40:00', 4, '1-16', '软件工程2班', '2024-2025-1', '2024-2025', 2.5, '专业必修', '学习软件开发的工程方法', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (17, '人工智能基础', 'CS114', 5, '实训室A101', '10:00:00', '11:40:00', 4, '1-16', '计算机科学与技术1班', '2024-2025-1', '2024-2025', 3.0, '专业选修', '学习人工智能的基本概念和算法', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (18, '信息安全管理', 'CS115', 6, '实训室C301', '14:00:00', '15:40:00', 4, '1-16', '信息安全1班', '2024-2025-1', '2024-2025', 2.0, '专业必修', '学习信息安全管理体系和标准', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (19, '网络设备配置', 'CS116', 5, '实训室D401', '16:00:00', '17:40:00', 4, '1-16', '网络工程1班', '2024-2025-1', '2024-2025', 2.5, '专业必修', '学习路由器和交换机配置', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');
INSERT INTO `course` VALUES (20, '体育课', 'PE103', 7, '体育馆', '19:00:00', '20:40:00', 4, '1-16', '信息安全1班', '2024-2025-1', '2024-2025', 1.0, '体育课程', '体育锻炼课程', 1, '2025-01-01 10:00:00', '2025-01-01 10:00:00');

-- ----------------------------
-- Table structure for leave_application
-- ----------------------------
DROP TABLE IF EXISTS `leave_application`;
CREATE TABLE `leave_application`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_id` int NOT NULL COMMENT '学生ID',
  `leave_type` enum('sick_leave','personal_leave','official_leave') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '请假类型',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `start_time` time NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` time NULL DEFAULT NULL COMMENT '结束时间',
  `reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '请假原因',
  `status` enum('pending','approved','rejected') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'pending' COMMENT '审批状态',
  `approved_by` int NULL DEFAULT NULL COMMENT '审批人ID',
  `approved_at` timestamp NULL DEFAULT NULL COMMENT '审批时间',
  `approval_comment` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '审批意见',
  `attachment` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件文件名',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_student_id`(`student_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_leave_type`(`leave_type` ASC) USING BTREE,
  INDEX `idx_start_date`(`start_date` ASC) USING BTREE,
  INDEX `idx_approved_by`(`approved_by` ASC) USING BTREE,
  CONSTRAINT `leave_application_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `leave_application_ibfk_2` FOREIGN KEY (`approved_by`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of leave_application
-- ----------------------------

-- ----------------------------
-- Table structure for login_log
-- ----------------------------
DROP TABLE IF EXISTS `login_log`;
CREATE TABLE `login_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NULL DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `login_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `success` tinyint(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_login_time`(`login_time` ASC) USING BTREE,
  CONSTRAINT `login_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of login_log
-- ----------------------------

-- ----------------------------
-- Table structure for semester_config
-- ----------------------------
DROP TABLE IF EXISTS `semester_config`;
CREATE TABLE `semester_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `semester` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `academic_year` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_date` date NOT NULL COMMENT '学期开始日期',
  `end_date` date NOT NULL COMMENT '学期结束日期',
  `total_weeks` int NULL DEFAULT 20 COMMENT '总周数',
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `semester`(`semester` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of semester_config
-- ----------------------------
INSERT INTO `semester_config` VALUES (1, '2024-2025-1', '2024-2025', '2024-09-02', '2025-01-19', 20, 1, '2025-07-23 17:49:24', '2025-07-23 17:49:24');
INSERT INTO `semester_config` VALUES (2, '2024-2025-2', '2024-2025', '2025-02-24', '2025-07-06', 20, 1, '2025-07-23 17:49:24', '2025-07-23 17:49:24');
INSERT INTO `semester_config` VALUES (3, '2025-2026-1', '2025-2026', '2025-09-01', '2026-01-18', 20, 1, '2025-07-23 17:49:24', '2025-07-23 17:49:24');
INSERT INTO `semester_config` VALUES (4, '2025-2026-2', '2025-2026', '2026-02-23', '2026-07-05', 20, 1, '2025-07-23 17:49:24', '2025-07-23 17:49:24');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `password_hash` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sex` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `grade` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '年级',
  `department` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '班级/部门',
  `number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '学号/教职号',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `role` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'student' COMMENT '角色权限',
  `face_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '人脸信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE,
  UNIQUE INDEX `number`(`number` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE,
  INDEX `idx_number`(`number` ASC) USING BTREE,
  INDEX `idx_role`(`role` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '<EMAIL>', 'scrypt:32768:8:1$2rXDH9QRMCdDAG7W$e709e73b5680ff5f410adca250415d108e4e3a9431daba082dee50cfaae6c22379c405165266c05d6de8241477155af2ba7b31ba95a38e20e27577e3e86e925b', '男', '1980-05-15', '教师', '管理部门', 'T001', '13800138001', 'admin', NULL, '2025-01-01 10:00:00', '2025-01-01 10:00:00', 1, NULL);
INSERT INTO `user` VALUES (2, 'john_brown', '<EMAIL>', 'scrypt:32768:8:1$2rXDH9QRMCdDAG7W$e709e73b5680ff5f410adca250415d108e4e3a9431daba082dee50cfaae6c22379c405165266c05d6de8241477155af2ba7b31ba95a38e20e27577e3e86e925b', '男', '2005-03-20', '大一', '计算机科学与技术1班', '2024001', '13800138002', 'student', NULL, '2025-01-02 10:00:00', '2025-01-02 10:00:00', 1, NULL);
INSERT INTO `user` VALUES (3, 'jim_green', '<EMAIL>', 'scrypt:32768:8:1$2rXDH9QRMCdDAG7W$e709e73b5680ff5f410adca250415d108e4e3a9431daba082dee50cfaae6c22379c405165266c05d6de8241477155af2ba7b31ba95a38e20e27577e3e86e925b', '男', '2004-08-12', '大二', '软件工程2班', '2023001', '13800138003', 'student', NULL, '2025-01-03 10:00:00', '2025-01-03 10:00:00', 1, NULL);
INSERT INTO `user` VALUES (4, 'mary_wang', '<EMAIL>', 'scrypt:32768:8:1$2rXDH9QRMCdDAG7W$e709e73b5680ff5f410adca250415d108e4e3a9431daba082dee50cfaae6c22379c405165266c05d6de8241477155af2ba7b31ba95a38e20e27577e3e86e925b', '女', '2003-11-08', '大三', '信息安全1班', '2022001', '13800138004', 'student', NULL, '2025-01-04 10:00:00', '2025-01-04 10:00:00', 1, NULL);
INSERT INTO `user` VALUES (5, 'teacher_li', '<EMAIL>', 'scrypt:32768:8:1$2rXDH9QRMCdDAG7W$e709e73b5680ff5f410adca250415d108e4e3a9431daba082dee50cfaae6c22379c405165266c05d6de8241477155af2ba7b31ba95a38e20e27577e3e86e925b', '女', '1985-12-03', '教师', '计算机学院', 'T002', '13800138005', 'teacher', NULL, '2025-01-05 10:00:00', '2025-01-05 10:00:00', 1, NULL);
INSERT INTO `user` VALUES (6, 'student_zhang', '<EMAIL>', 'scrypt:32768:8:1$2rXDH9QRMCdDAG7W$e709e73b5680ff5f410adca250415d108e4e3a9431daba082dee50cfaae6c22379c405165266c05d6de8241477155af2ba7b31ba95a38e20e27577e3e86e925b', '男', '2002-07-25', '大四', '网络工程1班', '2021001', '13800138006', 'student', NULL, '2025-01-06 10:00:00', '2025-01-06 10:00:00', 1, NULL);

SET FOREIGN_KEY_CHECKS = 1;
