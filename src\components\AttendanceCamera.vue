<template>
  <div class="attendance-camera">
    <div class="camera-container">
      <video 
        ref="videoRef" 
        v-show="cameraActive && !capturedImage"
        class="camera-video"
        autoplay
        muted
        playsinline
      ></video>
      
      <canvas 
        ref="canvasRef" 
        v-show="false"
        class="camera-canvas"
      ></canvas>

      <div v-if="capturedImage" class="captured-preview">
        <img :src="capturedImage" alt="拍摄的照片" class="captured-image" />
      </div>

      <div v-if="!cameraActive && !capturedImage" class="camera-placeholder">
        <camera-outlined class="placeholder-icon" />
        <p>点击开启摄像头进行人脸识别</p>
      </div>

      <!-- 识别状态覆盖层 -->
      <div v-if="recognizing" class="recognition-overlay">
        <div class="recognition-content">
          <loading-outlined class="recognition-icon" />
          <p>正在进行人脸识别...</p>
        </div>
      </div>
    </div>

    <div class="camera-controls">
      <a-button 
        v-if="!cameraActive && !capturedImage"
        type="primary"
        size="large"
        @click="startCamera"
        :loading="starting"
      >
        <camera-outlined />
        开启摄像头
      </a-button>

      <a-space v-if="cameraActive && !capturedImage" size="large">
        <a-button 
          type="primary" 
          size="large"
          @click="captureAndRecognize" 
          :loading="capturing"
        >
          <camera-outlined />
          拍照识别
        </a-button>
        <a-button 
          size="large"
          @click="stopCamera"
        >
          <stop-outlined />
          关闭摄像头
        </a-button>
      </a-space>

      <a-space v-if="capturedImage" size="large">
        <a-button 
          type="primary" 
          size="large"
          @click="confirmRecognition" 
          :loading="recognizing"
        >
          <check-outlined />
          确认识别
        </a-button>
        <a-button 
          size="large"
          @click="retakePhoto"
        >
          <redo-outlined />
          重新拍摄
        </a-button>
      </a-space>
    </div>

    <div class="status-display" v-if="status">
      <a-alert 
        :message="status" 
        :type="statusType" 
        show-icon 
        :closable="false"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  CameraOutlined, 
  StopOutlined, 
  RedoOutlined,
  CheckOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'AttendanceCamera',
  components: {
    CameraOutlined,
    StopOutlined,
    RedoOutlined,
    CheckOutlined,
    LoadingOutlined
  },
  emits: ['face-captured', 'recognition-result', 'error'],
  setup(props, { emit }) {
    const videoRef = ref(null)
    const canvasRef = ref(null)
    const cameraActive = ref(false)
    const starting = ref(false)
    const capturing = ref(false)
    const recognizing = ref(false)
    const capturedImage = ref(null)
    const status = ref('')
    const statusType = ref('info')
    
    let mediaStream = null

    // 开启摄像头
    const startCamera = async () => {
      starting.value = true
      status.value = '正在开启摄像头...'
      statusType.value = 'info'

      try {
        mediaStream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 },
            facingMode: 'user'
          },
          audio: false
        })

        if (videoRef.value) {
          videoRef.value.srcObject = mediaStream
          cameraActive.value = true
          status.value = '摄像头已开启，请将面部对准摄像头'
          statusType.value = 'success'
          message.success('摄像头开启成功')
        }
      } catch (error) {
        console.error('开启摄像头失败:', error)
        let errorMessage = '开启摄像头失败'
        
        if (error.name === 'NotAllowedError') {
          errorMessage = '摄像头权限被拒绝，请允许访问摄像头'
        } else if (error.name === 'NotFoundError') {
          errorMessage = '未找到摄像头设备'
        } else if (error.name === 'NotReadableError') {
          errorMessage = '摄像头被其他应用占用'
        }
        
        status.value = errorMessage
        statusType.value = 'error'
        message.error(errorMessage)
        emit('error', error)
      } finally {
        starting.value = false
      }
    }

    // 关闭摄像头
    const stopCamera = () => {
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop())
        mediaStream = null
      }
      
      if (videoRef.value) {
        videoRef.value.srcObject = null
      }
      
      cameraActive.value = false
      capturedImage.value = null
      status.value = ''
      message.info('摄像头已关闭')
    }

    // 拍照并开始识别
    const captureAndRecognize = () => {
      if (!videoRef.value || !canvasRef.value) {
        message.error('摄像头未准备就绪')
        return
      }

      capturing.value = true
      status.value = '正在拍照...'
      statusType.value = 'info'

      try {
        const video = videoRef.value
        const canvas = canvasRef.value
        const context = canvas.getContext('2d')

        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        context.drawImage(video, 0, 0, canvas.width, canvas.height)

        capturedImage.value = canvas.toDataURL('image/jpeg', 0.8)
        
        status.value = '拍照成功，请确认照片'
        statusType.value = 'success'
        
        emit('face-captured', {
          imageData: capturedImage.value,
          timestamp: new Date().toISOString()
        })

      } catch (error) {
        console.error('拍照失败:', error)
        status.value = '拍照失败，请重试'
        statusType.value = 'error'
        message.error('拍照失败')
      } finally {
        capturing.value = false
      }
    }

    // 重新拍摄
    const retakePhoto = () => {
      capturedImage.value = null
      status.value = '请将面部对准摄像头'
      statusType.value = 'info'
    }

    // 确认识别
    const confirmRecognition = async () => {
      if (!capturedImage.value) return

      recognizing.value = true
      status.value = '正在进行人脸识别，请稍候...'
      statusType.value = 'info'

      try {
        // 模拟人脸识别过程（实际应该调用后端API）
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // 模拟识别结果
        const mockResult = {
          success: Math.random() > 0.3, // 70%成功率
          confidence: Math.random() * 0.4 + 0.6, // 60%-100%置信度
          userId: 'mock_user_id',
          userName: '张三',
          imageData: capturedImage.value
        }

        if (mockResult.success) {
          status.value = `识别成功！用户：${mockResult.userName}`
          statusType.value = 'success'
          message.success('人脸识别成功')
        } else {
          status.value = '人脸识别失败，请重新拍摄'
          statusType.value = 'error'
          message.error('人脸识别失败')
        }

        emit('recognition-result', mockResult)
        
        // 清理状态
        setTimeout(() => {
          capturedImage.value = null
          status.value = ''
        }, 3000)

      } catch (error) {
        console.error('识别失败:', error)
        status.value = '识别过程出错，请重试'
        statusType.value = 'error'
        message.error('识别过程出错')
        emit('error', error)
      } finally {
        recognizing.value = false
      }
    }

    // 检查浏览器支持
    const checkBrowserSupport = () => {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        status.value = '当前浏览器不支持摄像头功能'
        statusType.value = 'error'
        return false
      }
      return true
    }

    onMounted(() => {
      checkBrowserSupport()
    })

    onUnmounted(() => {
      stopCamera()
    })

    return {
      videoRef,
      canvasRef,
      cameraActive,
      starting,
      capturing,
      recognizing,
      capturedImage,
      status,
      statusType,
      startCamera,
      stopCamera,
      captureAndRecognize,
      retakePhoto,
      confirmRecognition
    }
  }
}
</script>

<style scoped>
.attendance-camera {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.camera-container {
  position: relative;
  width: 100%;
  max-width: 640px;
  height: 480px;
  background: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid #e8e8e8;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.captured-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.captured-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.camera-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  text-align: center;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.recognition-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.recognition-content {
  text-align: center;
}

.recognition-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.camera-controls {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.status-display {
  width: 100%;
  max-width: 640px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-camera {
    padding: 16px;
  }
  
  .camera-container {
    height: 360px;
  }
  
  .camera-controls {
    flex-direction: column;
    width: 100%;
  }
  
  .camera-controls .ant-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .camera-container {
    height: 280px;
  }
  
  .placeholder-icon {
    font-size: 48px;
  }
}
</style>
