<template>
  <div class="attendance-records">
    <div class="header">
      <h2>签到记录管理</h2>
      <a-space>
        <a-button @click="exportRecords" :loading="exporting">
          <download-outlined />
          导出记录
        </a-button>
        <a-button type="primary" @click="refreshRecords" :loading="loading">
          <reload-outlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 筛选条件 -->
    <div class="filters">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select
            v-model:value="filters.course_id"
            placeholder="选择课程"
            allowClear
            style="width: 100%"
            @change="loadRecords"
          >
            <a-select-option value="">全部课程</a-select-option>
            <a-select-option v-for="course in courses" :key="course.id" :value="course.id">
              {{ course.course_name }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filters.status"
            placeholder="选择状态"
            allowClear
            style="width: 100%"
            @change="loadRecords"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="present">正常</a-select-option>
            <a-select-option value="late">迟到</a-select-option>
            <a-select-option value="absent">缺勤</a-select-option>
            <a-select-option value="truant">旷课</a-select-option>
            <a-select-option value="early_leave">早退</a-select-option>
            <a-select-option value="sick_leave">病假</a-select-option>
            <a-select-option value="personal_leave">事假</a-select-option>
            <a-select-option value="official_leave">公假</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-date-picker
            v-model:value="filters.date"
            placeholder="选择日期"
            style="width: 100%"
            @change="loadRecords"
          />
        </a-col>
        <a-col :span="6">
          <a-input
            v-model:value="filters.student_number"
            placeholder="输入学号搜索"
            @pressEnter="loadRecords"
            @change="debounceSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </a-col>
      </a-row>
    </div>

    <!-- 签到记录表格 -->
    <a-table
      :columns="columns"
      :data-source="records"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      :scroll="{ x: 1400 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'student_info'">
          <div class="student-info">
            <div class="student-name">{{ record.student_name }}</div>
            <div class="student-number">{{ record.student_number }}</div>
          </div>
        </template>
        
        <template v-if="column.key === 'course_info'">
          <div class="course-info">
            <div class="course-name">{{ record.course_name }}</div>
            <div class="course-time">{{ record.course_time }}</div>
          </div>
        </template>
        
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
          <div v-if="record.adjusted_at" class="adjusted-info">
            <small>已调整 {{ formatDateTime(record.adjusted_at) }}</small>
          </div>
        </template>
        
        <template v-if="column.key === 'check_in_time'">
          <div v-if="record.check_in_time" class="time-info">
            <div class="time">{{ formatTime(record.check_in_time) }}</div>
            <div class="date">{{ formatDate(record.check_in_time) }}</div>
          </div>
          <span v-else class="no-data">-</span>
        </template>
        
        <template v-if="column.key === 'check_out_time'">
          <div v-if="record.check_out_time" class="time-info">
            <div class="time">{{ formatTime(record.check_out_time) }}</div>
            <div class="date">{{ formatDate(record.check_out_time) }}</div>
          </div>
          <span v-else class="no-data">-</span>
        </template>
        
        <template v-if="column.key === 'duration'">
          <span v-if="record.check_in_time && record.check_out_time">
            {{ calculateDuration(record.check_in_time, record.check_out_time) }}
          </span>
          <span v-else class="no-data">-</span>
        </template>
        
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="viewRecord(record)">
              查看详情
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="editRecord(record)"
              v-if="canEdit"
            >
              编辑
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 记录详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="签到记录详情"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="学生姓名">
            {{ selectedRecord.student_name }}
          </a-descriptions-item>
          <a-descriptions-item label="学号">
            {{ selectedRecord.student_number }}
          </a-descriptions-item>
          <a-descriptions-item label="课程名称">
            {{ selectedRecord.course_name }}
          </a-descriptions-item>
          <a-descriptions-item label="上课时间">
            {{ selectedRecord.course_time }}
          </a-descriptions-item>
          <a-descriptions-item label="签到状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="签到时间">
            {{ selectedRecord.check_in_time ? formatDateTime(selectedRecord.check_in_time) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="签退时间">
            {{ selectedRecord.check_out_time ? formatDateTime(selectedRecord.check_out_time) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="持续时长">
            {{ selectedRecord.check_in_time && selectedRecord.check_out_time ? 
               calculateDuration(selectedRecord.check_in_time, selectedRecord.check_out_time) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(selectedRecord.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="最后更新" :span="2" v-if="selectedRecord.updated_at">
            {{ formatDateTime(selectedRecord.updated_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="调整信息" :span="2" v-if="selectedRecord.adjusted_at">
            <div>
              <p>调整时间: {{ formatDateTime(selectedRecord.adjusted_at) }}</p>
              <p v-if="selectedRecord.adjusted_by">调整人: {{ selectedRecord.adjusted_by }}</p>
              <p v-if="selectedRecord.adjustment_reason">调整原因: {{ selectedRecord.adjustment_reason }}</p>
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  DownloadOutlined, 
  ReloadOutlined, 
  SearchOutlined 
} from '@ant-design/icons-vue'
import { authUtils } from '../utils/api'
import dayjs from 'dayjs'

export default {
  name: 'AttendanceRecords',
  components: {
    DownloadOutlined,
    ReloadOutlined,
    SearchOutlined
  },
  setup() {
    const loading = ref(false)
    const exporting = ref(false)
    const detailModalVisible = ref(false)
    
    const records = ref([])
    const courses = ref([])
    const selectedRecord = ref(null)
    
    const currentUser = computed(() => authUtils.getCurrentUser())
    const canEdit = computed(() => {
      return currentUser.value && ['admin', 'teacher'].includes(currentUser.value.role)
    })

    const filters = reactive({
      course_id: '',
      status: '',
      date: null,
      student_number: ''
    })

    const pagination = reactive({
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
    })

    // 表格列定义
    const columns = [
      {
        title: '学生信息',
        key: 'student_info',
        width: 150,
        fixed: 'left'
      },
      {
        title: '课程信息',
        key: 'course_info',
        width: 200
      },
      {
        title: '状态',
        key: 'status',
        width: 120
      },
      {
        title: '签到时间',
        key: 'check_in_time',
        width: 150
      },
      {
        title: '签退时间',
        key: 'check_out_time',
        width: 150
      },
      {
        title: '持续时长',
        key: 'duration',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        fixed: 'right'
      }
    ]

    // 状态颜色映射
    const getStatusColor = (status) => {
      const colors = {
        present: 'green',
        late: 'orange',
        absent: 'red',
        truant: 'red',
        early_leave: 'orange',
        sick_leave: 'blue',
        personal_leave: 'purple',
        official_leave: 'cyan'
      }
      return colors[status] || 'default'
    }

    // 状态文本映射
    const getStatusText = (status) => {
      const texts = {
        present: '正常',
        late: '迟到',
        absent: '缺勤',
        truant: '旷课',
        early_leave: '早退',
        sick_leave: '病假',
        personal_leave: '事假',
        official_leave: '公假'
      }
      return texts[status] || status
    }

    // 格式化时间
    const formatTime = (dateTimeStr) => {
      return dayjs(dateTimeStr).format('HH:mm')
    }

    const formatDate = (dateTimeStr) => {
      return dayjs(dateTimeStr).format('MM-DD')
    }

    const formatDateTime = (dateTimeStr) => {
      return dayjs(dateTimeStr).format('YYYY-MM-DD HH:mm')
    }

    // 计算持续时长
    const calculateDuration = (startTime, endTime) => {
      const start = dayjs(startTime)
      const end = dayjs(endTime)
      const duration = end.diff(start, 'minute')
      const hours = Math.floor(duration / 60)
      const minutes = duration % 60
      return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`
    }

    // 加载课程列表
    const loadCourses = async () => {
      try {
        const response = await fetch('/api/courses', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          courses.value = data.courses || []
        }
      } catch (error) {
        console.error('加载课程列表失败:', error)
      }
    }

    // 加载签到记录
    const loadRecords = async () => {
      loading.value = true
      try {
        const params = new URLSearchParams()
        params.append('page', pagination.current)
        params.append('page_size', pagination.pageSize)

        if (filters.course_id) params.append('course_id', filters.course_id)
        if (filters.status) params.append('status', filters.status)
        if (filters.date) params.append('date', filters.date.format('YYYY-MM-DD'))
        if (filters.student_number) params.append('student_number', filters.student_number)

        const response = await fetch(`/api/attendance/records?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          records.value = data.records || []
          pagination.total = data.total || 0
        } else {
          const error = await response.json()
          message.error(error.error || '加载记录失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        loading.value = false
      }
    }

    // 刷新记录
    const refreshRecords = () => {
      pagination.current = 1
      loadRecords()
    }

    // 表格变化处理
    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      loadRecords()
    }

    // 搜索防抖
    let searchTimeout = null
    const debounceSearch = () => {
      if (searchTimeout) clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        pagination.current = 1
        loadRecords()
      }, 500)
    }

    // 查看记录详情
    const viewRecord = (record) => {
      selectedRecord.value = record
      detailModalVisible.value = true
    }

    // 编辑记录
    const editRecord = (record) => {
      // 这里可以跳转到编辑页面或打开编辑模态框
      message.info('编辑功能待实现')
    }

    // 导出记录
    const exportRecords = async () => {
      exporting.value = true
      try {
        const params = new URLSearchParams()
        if (filters.course_id) params.append('course_id', filters.course_id)
        if (filters.status) params.append('status', filters.status)
        if (filters.date) params.append('date', filters.date.format('YYYY-MM-DD'))
        if (filters.student_number) params.append('student_number', filters.student_number)

        const response = await fetch(`/api/attendance/export?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `attendance_records_${dayjs().format('YYYY-MM-DD')}.xlsx`
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(url)
          message.success('导出成功')
        } else {
          message.error('导出失败')
        }
      } catch (error) {
        message.error('导出失败')
      } finally {
        exporting.value = false
      }
    }

    onMounted(() => {
      loadCourses()
      loadRecords()
    })

    return {
      loading,
      exporting,
      detailModalVisible,
      records,
      courses,
      selectedRecord,
      canEdit,
      filters,
      pagination,
      columns,
      getStatusColor,
      getStatusText,
      formatTime,
      formatDate,
      formatDateTime,
      calculateDuration,
      loadRecords,
      refreshRecords,
      handleTableChange,
      debounceSearch,
      viewRecord,
      editRecord,
      exportRecords
    }
  }
}
</script>

<style scoped>
.attendance-records {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 80px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.filters {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 学生信息样式 */
.student-info {
  display: flex;
  flex-direction: column;
}

.student-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.student-number {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

/* 课程信息样式 */
.course-info {
  display: flex;
  flex-direction: column;
}

.course-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.course-time {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.date {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.no-data {
  color: #ccc;
  font-style: italic;
}

/* 调整信息样式 */
.adjusted-info {
  margin-top: 4px;
}

.adjusted-info small {
  color: #ff7875;
  font-size: 11px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.ant-table-thead > tr > th) {
  background: #f8f9fa;
  font-weight: 600;
  font-size: 16px;
  color: #333;
  border-bottom: 2px solid #e8e8e8;
}

:deep(.ant-table-tbody > tr > td) {
  font-size: 14px;
  padding: 16px 12px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f0f8ff;
}

/* 按钮样式优化 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 模态框样式优化 */
:deep(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

:deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

:deep(.ant-modal-close) {
  color: white;
}

:deep(.ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.ant-modal-body) {
  padding: 24px;
}

/* 输入框样式优化 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-records {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filters {
    padding: 16px;
  }

  :deep(.ant-table-tbody > tr > td) {
    font-size: 12px;
    padding: 12px 8px;
  }

  .student-info, .course-info, .time-info {
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .attendance-records {
    padding: 12px;
  }

  .header h2 {
    font-size: 20px;
  }

  :deep(.ant-modal-body) {
    padding: 16px;
  }
}
</style>
