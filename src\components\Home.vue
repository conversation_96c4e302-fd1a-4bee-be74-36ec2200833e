<template>
  <div class="home">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1>欢迎使用智慧管理系统</h1>
        <p>基于人工智能的实训室智慧管理系统</p>
        <div class="user-info">
          <a-avatar :size="64" :src="getUserAvatarUrl()" />
          <div class="user-details">
            <h3>{{ user?.username }}</h3>
            <p>{{ getRoleText(user?.role) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能导航区域 -->
    <div class="navigation-section">
      <h2>功能导航</h2>
      <div class="nav-grid">
        <!-- 学生功能 -->
        <div v-if="isStudent" class="nav-category">
          <h3>学生功能</h3>
          <div class="nav-cards">
            <div class="nav-card" @click="navigateTo('/attendance')">
              <div class="nav-icon">
                <camera-outlined />
              </div>
              <h4>签到签退</h4>
              <p>人脸识别签到签退</p>
            </div>
            <div class="nav-card" @click="navigateTo('/leave')">
              <div class="nav-icon">
                <file-text-outlined />
              </div>
              <h4>请假申请</h4>
              <p>在线申请请假</p>
            </div>
            <div class="nav-card" @click="navigateTo('/profile')">
              <div class="nav-icon">
                <user-outlined />
              </div>
              <h4>个人信息</h4>
              <p>查看和编辑个人信息</p>
            </div>
          </div>
        </div>

        <!-- 教师功能 -->
        <div v-if="isTeacher" class="nav-category">
          <h3>教师功能</h3>
          <div class="nav-cards">
            <div class="nav-card" @click="navigateTo('/courses')">
              <div class="nav-icon">
                <book-outlined />
              </div>
              <h4>课程管理</h4>
              <p>管理课程信息</p>
            </div>
            <div class="nav-card" @click="navigateTo('/attendance')">
              <div class="nav-icon">
                <camera-outlined />
              </div>
              <h4>签到管理</h4>
              <p>生成签退码，调整状态</p>
            </div>
            <div class="nav-card" @click="navigateTo('/statistics')">
              <div class="nav-icon">
                <bar-chart-outlined />
              </div>
              <h4>签到统计</h4>
              <p>查看签到统计数据</p>
            </div>
            <div class="nav-card" @click="navigateTo('/records')">
              <div class="nav-icon">
                <table-outlined />
              </div>
              <h4>签到记录</h4>
              <p>查看详细签到记录</p>
            </div>
            <div class="nav-card" @click="navigateTo('/leave')">
              <div class="nav-icon">
                <file-text-outlined />
              </div>
              <h4>请假审批</h4>
              <p>审批学生请假申请</p>
            </div>
          </div>
        </div>

        <!-- 管理员功能 -->
        <div v-if="isAdmin" class="nav-category">
          <h3>管理员功能</h3>
          <div class="nav-cards">
            <div class="nav-card" @click="navigateTo('/info')">
              <div class="nav-icon">
                <team-outlined />
              </div>
              <h4>用户管理</h4>
              <p>管理系统用户</p>
            </div>
            <div class="nav-card" @click="navigateTo('/face-management')">
              <div class="nav-icon">
                <scan-outlined />
              </div>
              <h4>人脸管理</h4>
              <p>管理用户人脸信息</p>
            </div>
            <div class="nav-card" @click="navigateTo('/safety')">
              <div class="nav-icon">
                <safety-outlined />
              </div>
              <h4>安全监控</h4>
              <p>实训室安全事件识别</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速统计区域 -->
    <div v-if="isTeacher || isAdmin" class="stats-section">
      <h2>今日概览</h2>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic title="今日签到" :value="todayStats.total_checkins" :value-style="{ color: '#3f8600' }">
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="迟到人数" :value="todayStats.late_count" :value-style="{ color: '#cf1322' }">
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="请假申请" :value="todayStats.leave_applications" :value-style="{ color: '#1890ff' }">
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="出勤率" :value="todayStats.attendance_rate" suffix="%" :value-style="{ color: '#722ed1' }">
              <template #prefix>
                <pie-chart-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  CameraOutlined,
  FileTextOutlined,
  UserOutlined,
  BookOutlined,
  BarChartOutlined,
  TableOutlined,
  TeamOutlined,
  ScanOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PieChartOutlined
} from '@ant-design/icons-vue'
import { authUtils, authAPI } from '../utils/api'

export default {
  name: 'Home',
  components: {
    CameraOutlined,
    FileTextOutlined,
    UserOutlined,
    BookOutlined,
    BarChartOutlined,
    TableOutlined,
    TeamOutlined,
    ScanOutlined,
    SafetyOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    PieChartOutlined
  },
  setup() {
    const router = useRouter()
    const todayStats = ref({
      total_checkins: 0,
      late_count: 0,
      leave_applications: 0,
      attendance_rate: 0
    })

    const user = computed(() => authUtils.getCurrentUser())

    const isStudent = computed(() => {
      return user.value && user.value.role === 'student'
    })

    const isTeacher = computed(() => {
      return user.value && ['admin', 'teacher'].includes(user.value.role)
    })

    const isAdmin = computed(() => {
      return user.value && user.value.role === 'admin'
    })

    // 获取用户头像URL
    const getUserAvatarUrl = () => {
      if (user.value && user.value.avatar) {
        return authAPI.getAvatarUrl(user.value.avatar)
      }
      return '/default-avatar.png'
    }

    // 获取角色文本
    const getRoleText = (role) => {
      const roleTexts = {
        student: '学生',
        teacher: '教师',
        admin: '管理员'
      }
      return roleTexts[role] || role
    }

    // 导航到指定页面
    const navigateTo = (path) => {
      router.push(path)
    }

    // 加载今日统计数据
    const loadTodayStats = async () => {
      if (!isTeacher.value && !isAdmin.value) return

      try {
        const response = await fetch('/api/dashboard/today-stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          todayStats.value = data.stats || todayStats.value
        }
      } catch (error) {
        console.error('加载今日统计失败:', error)
      }
    }

    onMounted(() => {
      loadTodayStats()
    })

    return {
      user,
      isStudent,
      isTeacher,
      isAdmin,
      todayStats,
      getUserAvatarUrl,
      getRoleText,
      navigateTo
    }
  }
}
</script>

<style scoped>
.home {
  padding: 24px;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 50%, #f6ffed 100%);
  min-height: calc(100vh - 80px);
}

/* 欢迎区域 */
.welcome-section {
  background: white;
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 32px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  text-align: center;
}

.welcome-content h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1890ff;
  margin: 0 0 12px 0;
}

.welcome-content p {
  font-size: 18px;
  color: #666;
  margin: 0 0 32px 0;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.user-details h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.user-details p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 导航区域 */
.navigation-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.navigation-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 24px 0;
  text-align: center;
}

.nav-grid {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.nav-category h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e8f4fd;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.nav-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
  border-color: #1890ff;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

.nav-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 12px;
}

.nav-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.nav-card p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* 统计区域 */
.stats-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stats-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 24px 0;
  text-align: center;
}

/* 统计卡片样式优化 */
:deep(.ant-card) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

:deep(.ant-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home {
    padding: 16px;
  }

  .welcome-section {
    padding: 24px;
    margin-bottom: 24px;
  }

  .welcome-content h1 {
    font-size: 24px;
  }

  .welcome-content p {
    font-size: 16px;
  }

  .user-info {
    flex-direction: column;
    gap: 12px;
  }

  .navigation-section {
    padding: 24px;
    margin-bottom: 24px;
  }

  .nav-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }

  .nav-card {
    padding: 20px;
  }

  .nav-icon {
    font-size: 28px;
  }

  .stats-section {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .home {
    padding: 12px;
  }

  .welcome-section {
    padding: 20px;
  }

  .welcome-content h1 {
    font-size: 20px;
  }

  .navigation-section {
    padding: 20px;
  }

  .nav-cards {
    grid-template-columns: 1fr;
  }

  .stats-section {
    padding: 20px;
  }

  :deep(.ant-col) {
    margin-bottom: 16px;
  }
}
</style>