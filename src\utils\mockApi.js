// 模拟API数据，用于演示系统功能
// 在实际项目中，这些数据应该从后端数据库获取

// 模拟用户数据
export const mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    number: 'A001',
    real_name: '系统管理员',
    department: '信息技术部',
    phone: '13800138000',
    birth_date: '1985-01-01',
    gender: 'male',
    avatar: null,
    face_info: null,
    is_active: true
  },
  {
    id: 2,
    username: 'teacher1',
    email: '<EMAIL>',
    role: 'teacher',
    number: 'T001',
    real_name: '张老师',
    department: '计算机学院',
    phone: '13800138001',
    birth_date: '1980-05-15',
    gender: 'male',
    avatar: null,
    face_info: null,
    is_active: true
  },
  {
    id: 3,
    username: 'teacher2',
    email: '<EMAIL>',
    role: 'teacher',
    number: 'T002',
    real_name: '李老师',
    department: '计算机学院',
    phone: '13800138002',
    birth_date: '1982-08-20',
    gender: 'female',
    avatar: null,
    face_info: null,
    is_active: true
  },
  {
    id: 4,
    username: 'student1',
    email: '<EMAIL>',
    role: 'student',
    number: '2021001',
    real_name: '王小明',
    department: '计算机科学与技术1班',
    phone: '13800138003',
    birth_date: '2003-03-10',
    gender: 'male',
    avatar: null,
    face_info: null,
    is_active: true
  },
  {
    id: 5,
    username: 'student2',
    email: '<EMAIL>',
    role: 'student',
    number: '2021002',
    real_name: '李小红',
    department: '计算机科学与技术1班',
    phone: '13800138004',
    birth_date: '2003-07-22',
    gender: 'female',
    avatar: null,
    face_info: null,
    is_active: true
  },
  {
    id: 6,
    username: 'student3',
    email: '<EMAIL>',
    role: 'student',
    number: '2021003',
    real_name: '张小华',
    department: '软件工程2班',
    phone: '13800138005',
    birth_date: '2003-11-05',
    gender: 'male',
    avatar: null,
    face_info: null,
    is_active: true
  }
]

// 模拟课程数据
export const mockCourses = [
  {
    id: 1,
    course_name: '数据结构与算法',
    course_code: 'CS101',
    teacher_id: 2,
    teacher_name: '张老师',
    classroom: '实训室A',
    day_of_week: 1,
    start_time: '08:00:00',
    end_time: '10:00:00',
    department: '计算机科学与技术1班',
    semester: '2024-2025-1',
    academic_year: '2024-2025',
    credits: 3.0,
    course_type: '专业必修',
    weeks: '1-16',
    description: '学习基本的数据结构和算法设计',
    is_active: true
  },
  {
    id: 2,
    course_name: 'Web开发技术',
    course_code: 'CS102',
    teacher_id: 2,
    teacher_name: '张老师',
    classroom: '实训室B',
    day_of_week: 2,
    start_time: '14:00:00',
    end_time: '16:00:00',
    department: '软件工程2班',
    semester: '2024-2025-1',
    academic_year: '2024-2025',
    credits: 2.5,
    course_type: '专业必修',
    weeks: '1-16',
    description: '学习前端和后端Web开发技术',
    is_active: true
  },
  {
    id: 3,
    course_name: '网络安全基础',
    course_code: 'CS103',
    teacher_id: 3,
    teacher_name: '李老师',
    classroom: '实训室C',
    day_of_week: 3,
    start_time: '10:00:00',
    end_time: '12:00:00',
    department: '信息安全1班',
    semester: '2024-2025-1',
    academic_year: '2024-2025',
    credits: 3.0,
    course_type: '专业必修',
    weeks: '1-16',
    description: '学习网络安全的基本概念和技术',
    is_active: true
  }
]

// 模拟签到记录数据
export const mockAttendanceRecords = [
  {
    id: 1,
    student_id: 4,
    student_name: '王小明',
    student_number: '2021001',
    course_id: 1,
    course_name: '数据结构与算法',
    classroom: '实训室A',
    check_in_time: '2024-01-20 08:05:00',
    check_out_time: '2024-01-20 09:55:00',
    status: 'late',
    session_code: null,
    adjusted_at: null,
    adjusted_by: null,
    adjustment_reason: null,
    created_at: '2024-01-20 08:05:00'
  },
  {
    id: 2,
    student_id: 5,
    student_name: '李小红',
    student_number: '2021002',
    course_id: 1,
    course_name: '数据结构与算法',
    classroom: '实训室A',
    check_in_time: '2024-01-20 07:58:00',
    check_out_time: '2024-01-20 09:58:00',
    status: 'present',
    session_code: null,
    adjusted_at: null,
    adjusted_by: null,
    adjustment_reason: null,
    created_at: '2024-01-20 07:58:00'
  },
  {
    id: 3,
    student_id: 6,
    student_name: '张小华',
    student_number: '2021003',
    course_id: 2,
    course_name: 'Web开发技术',
    classroom: '实训室B',
    check_in_time: '2024-01-20 14:02:00',
    check_out_time: '2024-01-20 15:58:00',
    status: 'present',
    session_code: null,
    adjusted_at: null,
    adjusted_by: null,
    adjustment_reason: null,
    created_at: '2024-01-20 14:02:00'
  }
]

// 模拟请假申请数据
export const mockLeaveApplications = [
  {
    id: 1,
    student_id: 5,
    student_name: '李小红',
    student_number: '2021002',
    leave_type: 'sick_leave',
    start_date: '2024-01-21',
    end_date: '2024-01-21',
    start_time: '08:00:00',
    end_time: '10:00:00',
    reason: '感冒发烧，需要休息',
    status: 'approved',
    approved_by: 2,
    approved_by_name: '张老师',
    approved_at: '2024-01-21 07:30:00',
    rejection_reason: null,
    created_at: '2024-01-21 07:00:00'
  },
  {
    id: 2,
    student_id: 6,
    student_name: '张小华',
    student_number: '2021003',
    leave_type: 'personal_leave',
    start_date: '2024-01-25',
    end_date: '2024-01-25',
    start_time: '14:00:00',
    end_time: '16:00:00',
    reason: '家中有事需要处理',
    status: 'pending',
    approved_by: null,
    approved_by_name: null,
    approved_at: null,
    rejection_reason: null,
    created_at: '2024-01-25 10:00:00'
  }
]

// 模拟安全事件数据
export const mockSafetyEvents = [
  {
    id: 1,
    event_type: 'unauthorized_access',
    location: '实训室A',
    device_name: '摄像头A-02',
    description: '检测到未授权人员进入实训室',
    image_url: '/api/events/image/1',
    confidence_score: 0.85,
    status: 'pending',
    handled_by: null,
    handled_by_name: null,
    handled_at: null,
    resolution: null,
    created_at: '2024-01-20 14:30:00'
  },
  {
    id: 2,
    event_type: 'equipment_misuse',
    location: '实训室B',
    device_name: '摄像头B-01',
    description: '检测到设备操作不当',
    image_url: '/api/events/image/2',
    confidence_score: 0.78,
    status: 'resolved',
    handled_by: 2,
    handled_by_name: '张老师',
    handled_at: '2024-01-20 13:20:00',
    resolution: '已提醒学生正确操作设备',
    created_at: '2024-01-20 13:15:00'
  }
]

// API模拟函数
export const mockAPI = {
  // 用户相关
  async getUsers(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
    let users = [...mockUsers]
    
    if (params.role) {
      users = users.filter(user => user.role === params.role)
    }
    
    if (params.search) {
      users = users.filter(user => 
        user.real_name.includes(params.search) || 
        user.number.includes(params.search)
      )
    }
    
    return {
      users,
      total: users.length
    }
  },

  // 课程相关
  async getCourses(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 500))
    let courses = [...mockCourses]
    
    if (params.teacher_id) {
      courses = courses.filter(course => course.teacher_id === parseInt(params.teacher_id))
    }
    
    return {
      courses,
      total: courses.length
    }
  },

  // 签到记录相关
  async getAttendanceRecords(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 500))
    let records = [...mockAttendanceRecords]
    
    if (params.course_id) {
      records = records.filter(record => record.course_id === parseInt(params.course_id))
    }
    
    if (params.status) {
      records = records.filter(record => record.status === params.status)
    }
    
    if (params.student_number) {
      records = records.filter(record => record.student_number.includes(params.student_number))
    }
    
    return {
      records,
      total: records.length
    }
  },

  // 请假申请相关
  async getLeaveApplications(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 500))
    let applications = [...mockLeaveApplications]
    
    if (params.status) {
      applications = applications.filter(app => app.status === params.status)
    }
    
    if (params.student_id) {
      applications = applications.filter(app => app.student_id === parseInt(params.student_id))
    }
    
    return {
      applications,
      total: applications.length
    }
  },

  // 安全事件相关
  async getSafetyEvents(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 500))
    let events = [...mockSafetyEvents]
    
    if (params.event_type) {
      events = events.filter(event => event.event_type === params.event_type)
    }
    
    if (params.status) {
      events = events.filter(event => event.status === params.status)
    }
    
    return {
      events,
      total: events.length
    }
  }
}
