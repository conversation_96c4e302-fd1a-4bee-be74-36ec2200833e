<template>
  <div class="face-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">
            <camera-outlined class="title-icon" />
            人脸信息管理
          </h2>
          <p class="page-description">批量管理系统中的用户人脸信息</p>
        </div>
        <div class="header-right">
          <button @click="goBack" class="back-btn">
            <arrow-left-outlined />
            返回
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-container">
      <div class="stat-card">
        <div class="stat-icon users">
          <user-outlined />
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon faces">
          <camera-outlined />
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ usersWithFace }}</div>
          <div class="stat-label">已设置人脸</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon pending">
          <exclamation-circle-outlined />
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ usersWithoutFace }}</div>
          <div class="stat-label">未设置人脸</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rate">
          <pie-chart-outlined />
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ faceCompletionRate }}%</div>
          <div class="stat-label">完成率</div>
        </div>
      </div>
    </div>

    <!-- 筛选和操作区域 -->
    <div class="filter-section">
      <div class="filter-left">
        <a-select
          v-model:value="filterStatus"
          placeholder="筛选状态"
          style="width: 150px"
          @change="handleFilterChange"
        >
          <a-select-option value="all">全部用户</a-select-option>
          <a-select-option value="with-face">已设置人脸</a-select-option>
          <a-select-option value="without-face">未设置人脸</a-select-option>
        </a-select>
        
        <a-select
          v-model:value="filterRole"
          placeholder="筛选角色"
          style="width: 120px"
          @change="handleFilterChange"
        >
          <a-select-option value="all">全部角色</a-select-option>
          <a-select-option value="admin">管理员</a-select-option>
          <a-select-option value="teacher">教师</a-select-option>
          <a-select-option value="student">学生</a-select-option>
        </a-select>

        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索用户名或学号"
          style="width: 200px"
          @search="handleSearch"
          @change="handleSearch"
        />
      </div>
      
      <div class="filter-right">
        <a-button type="primary" @click="refreshData" :loading="loading">
          <reload-outlined />
          刷新数据
        </a-button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-grid" v-if="!loading">
      <div 
        v-for="user in filteredUsers" 
        :key="user.id"
        class="user-card"
        :class="{ 'has-face': user.face_info }"
      >
        <div class="user-header">
          <div class="user-avatar">
            <img 
              v-if="user.avatar" 
              :src="getAvatarUrl(user.avatar)" 
              :alt="user.username + '的头像'"
              class="avatar-img"
            />
            <div v-else class="no-avatar">
              <user-outlined />
            </div>
          </div>
          <div class="user-info">
            <h4 class="user-name">{{ user.username }}</h4>
            <p class="user-details">{{ user.number }} | {{ user.role === 'admin' ? '管理员' : user.role === 'teacher' ? '教师' : '学生' }}</p>
          </div>
        </div>

        <div class="face-section">
          <div class="face-preview" v-if="user.face_info">
            <img 
              :src="getFaceUrl(user.face_info)" 
              :alt="user.username + '的人脸'"
              class="face-image"
              @error="handleFaceError"
            />
            <div class="face-status success">
              <check-circle-outlined />
              已设置人脸
            </div>
          </div>
          <div class="no-face-preview" v-else>
            <div class="face-placeholder">
              <camera-outlined />
            </div>
            <div class="face-status pending">
              <exclamation-circle-outlined />
              未设置人脸
            </div>
          </div>
        </div>

        <div class="user-actions">
          <a-button 
            size="small" 
            type="primary" 
            @click="manageFace(user)"
          >
            <camera-outlined />
            {{ user.face_info ? '更新人脸' : '设置人脸' }}
          </a-button>
          <a-button 
            v-if="user.face_info"
            size="small" 
            danger 
            @click="deleteFace(user)"
          >
            <delete-outlined />
            删除
          </a-button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <div class="loading-spinner"></div>
      <p>加载用户数据中...</p>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!loading && filteredUsers.length === 0">
      <div class="empty-icon">
        <user-outlined />
      </div>
      <h3>没有找到用户</h3>
      <p>请尝试调整筛选条件或搜索关键词</p>
    </div>

    <!-- 人脸管理模态框 -->
    <a-modal
      v-model:open="faceModalVisible"
      :title="`${selectedUser?.username} - 人脸管理`"
      width="700px"
      :footer="null"
      @cancel="closeFaceModal"
    >
      <FaceCapture
        v-if="selectedUser"
        :user-id="selectedUser.id"
        :current-face-info="selectedUser.face_info"
        @face-updated="handleFaceUpdated"
        @face-deleted="handleFaceDeleted"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { 
  CameraOutlined, 
  ArrowLeftOutlined, 
  UserOutlined,
  ExclamationCircleOutlined,
  PieChartOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import { 
  Select as ASelect, 
  SelectOption as ASelectOption,
  InputSearch as AInputSearch,
  Button as AButton,
  Modal as AModal,
  message 
} from 'ant-design-vue';
import { authAPI } from '../utils/api';
import FaceCapture from './FaceCapture.vue';

const router = useRouter();

// 响应式数据
const users = ref([]);
const loading = ref(true);
const faceModalVisible = ref(false);
const selectedUser = ref(null);
const filterStatus = ref('all');
const filterRole = ref('all');
const searchKeyword = ref('');

// 计算属性
const totalUsers = computed(() => users.value.length);
const usersWithFace = computed(() => users.value.filter(user => user.face_info).length);
const usersWithoutFace = computed(() => users.value.filter(user => !user.face_info).length);
const faceCompletionRate = computed(() => {
  if (totalUsers.value === 0) return 0;
  return Math.round((usersWithFace.value / totalUsers.value) * 100);
});

const filteredUsers = computed(() => {
  let filtered = users.value;

  // 状态筛选
  if (filterStatus.value === 'with-face') {
    filtered = filtered.filter(user => user.face_info);
  } else if (filterStatus.value === 'without-face') {
    filtered = filtered.filter(user => !user.face_info);
  }

  // 角色筛选
  if (filterRole.value !== 'all') {
    filtered = filtered.filter(user => user.role === filterRole.value);
  }

  // 搜索筛选
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(user => 
      user.username.toLowerCase().includes(keyword) ||
      (user.number && user.number.toLowerCase().includes(keyword))
    );
  }

  return filtered;
});

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true;
  try {
    const response = await authAPI.getUsersList();
    users.value = response.data.users;
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  fetchUsers();
};

// 处理筛选变化
const handleFilterChange = () => {
  // 筛选逻辑已在计算属性中处理
};

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

// 获取头像URL
const getAvatarUrl = (filename) => {
  return authAPI.getAvatarUrl(filename);
};

// 获取人脸图片URL
const getFaceUrl = (filename) => {
  return authAPI.getFaceUrl(filename);
};

// 处理人脸图片加载错误
const handleFaceError = (event) => {
  event.target.style.display = 'none';
};

// 人脸管理
const manageFace = (user) => {
  selectedUser.value = user;
  faceModalVisible.value = true;
};

// 关闭人脸管理模态框
const closeFaceModal = () => {
  faceModalVisible.value = false;
  selectedUser.value = null;
};

// 处理人脸更新
const handleFaceUpdated = (faceInfo) => {
  if (selectedUser.value) {
    const userIndex = users.value.findIndex(u => u.id === selectedUser.value.id);
    if (userIndex !== -1) {
      users.value[userIndex].face_info = faceInfo;
      selectedUser.value.face_info = faceInfo;
    }
  }
  message.success('人脸信息更新成功');
};

// 处理人脸删除
const handleFaceDeleted = () => {
  if (selectedUser.value) {
    const userIndex = users.value.findIndex(u => u.id === selectedUser.value.id);
    if (userIndex !== -1) {
      users.value[userIndex].face_info = null;
      selectedUser.value.face_info = null;
    }
  }
  message.success('人脸信息删除成功');
};

// 删除人脸
const deleteFace = async (user) => {
  try {
    await authAPI.adminDeleteUserFace(user.id);
    message.success('人脸信息删除成功');
    user.face_info = null;
  } catch (error) {
    console.error('删除失败:', error);
    message.error(error.response?.data?.error || '删除失败');
  }
};

// 返回
const goBack = () => {
  router.go(-1);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.face-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 28px;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.back-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f0f8ff;
}

/* 统计卡片样式 */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.users {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.faces {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #d46b08;
}

.stat-icon.rate {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #1890ff;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 筛选区域样式 */
.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-right {
  display: flex;
  gap: 12px;
}

/* 用户网格样式 */
.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.user-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  border: 2px solid transparent;
}

.user-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.user-card.has-face {
  border-color: #52c41a;
}

.user-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e8e8e8;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-avatar {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20px;
}

.user-info {
  flex: 1;
}

.user-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.user-details {
  margin: 0;
  font-size: 12px;
  color: #666;
}

/* 人脸预览区域 */
.face-section {
  margin-bottom: 16px;
}

.face-preview {
  text-align: center;
}

.face-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid #52c41a;
  margin-bottom: 8px;
}

.no-face-preview {
  text-align: center;
}

.face-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  background: #f5f5f5;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  color: #999;
  font-size: 32px;
}

.face-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.face-status.success {
  color: #52c41a;
}

.face-status.pending {
  color: #faad14;
}

/* 用户操作按钮 */
.user-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.user-actions .ant-btn {
  font-size: 12px;
  height: 32px;
  padding: 0 12px;
}

/* 加载和空状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.empty-state p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .face-management {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-left {
    flex-direction: column;
    gap: 12px;
  }

  .users-grid {
    grid-template-columns: 1fr;
  }

  .user-actions {
    flex-direction: column;
  }

  .user-actions .ant-btn {
    width: 100%;
  }
}
</style>
