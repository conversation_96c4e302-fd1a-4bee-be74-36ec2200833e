<template>
  <div class="attendance-check">
    <div class="header">
      <h2>签到签退</h2>
    </div>

    <!-- 签到区域 -->
    <div class="check-section">
      <a-card title="学生签到" class="check-card">
        <a-form layout="vertical" @finish="handleCheckIn">
          <a-form-item label="选择课程" name="course_id" :rules="[{ required: true, message: '请选择课程' }]">
            <a-select v-model:value="checkInForm.course_id" placeholder="请选择课程" style="width: 100%">
              <a-select-option v-for="course in availableCourses" :key="course.id" :value="course.id">
                {{ course.course_name }} - {{ course.classroom }} ({{ getDayName(course.day_of_week) }} {{ course.start_time }}-{{ course.end_time }})
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="学号" name="student_number" :rules="[{ required: true, message: '请输入学号' }]">
            <a-input v-model:value="checkInForm.student_number" placeholder="请输入学号" size="large" />
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              size="large"
              :loading="checkingIn"
              block
              @click="startFaceRecognitionCheckIn"
              :disabled="!checkInForm.course_id || !checkInForm.student_number"
            >
              <CameraOutlined />
              人脸识别签到
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 人脸识别模态框 -->
        <a-modal
          v-model:open="faceRecognitionModalVisible"
          title="人脸识别签到"
          :footer="null"
          width="800px"
          :maskClosable="false"
        >
          <AttendanceCamera
            @face-captured="onFaceCaptured"
            @recognition-result="onRecognitionResult"
            @error="onCameraError"
          />
        </a-modal>
      </a-card>

      <!-- 签退区域 -->
      <a-card title="学生签退" class="check-card">
        <a-form layout="vertical" @finish="handleCheckOut">
          <a-form-item label="学号" name="student_number" :rules="[{ required: true, message: '请输入学号' }]">
            <a-input v-model:value="checkOutForm.student_number" placeholder="请输入学号" size="large" />
          </a-form-item>
          
          <a-form-item label="签退码" name="session_code" :rules="[{ required: true, message: '请输入签退码' }]">
            <a-input v-model:value="checkOutForm.session_code" placeholder="请输入教师发布的签退码" size="large" />
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              size="large"
              :loading="checkingOut"
              block
              @click="startFaceRecognitionCheckOut"
              :disabled="!checkOutForm.session_code || !checkOutForm.student_number"
            >
              <CameraOutlined />
              人脸识别签退
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 人脸识别签退模态框 -->
        <a-modal
          v-model:open="faceRecognitionCheckoutModalVisible"
          title="人脸识别签退"
          :footer="null"
          width="800px"
          :maskClosable="false"
        >
          <AttendanceCamera
            @face-captured="onFaceCheckoutCaptured"
            @recognition-result="onCheckoutRecognitionResult"
            @error="onCameraError"
          />
        </a-modal>
      </a-card>
    </div>

    <!-- 教师功能区域 -->
    <div v-if="isTeacher" class="teacher-section">
      <a-card title="教师功能" class="teacher-card">
        <a-space direction="vertical" style="width: 100%">
          <!-- 生成签退码 -->
          <div class="checkout-code-section">
            <h4>生成签退码</h4>
            <a-form layout="inline" @finish="generateCheckoutCode">
              <a-form-item label="选择课程" name="course_id" :rules="[{ required: true, message: '请选择课程' }]">
                <a-select v-model:value="codeForm.course_id" placeholder="请选择课程" style="width: 300px">
                  <a-select-option v-for="course in myCourses" :key="course.id" :value="course.id">
                    {{ course.course_name }} - {{ course.department }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="有效时长(分钟)" name="duration_minutes">
                <a-input-number v-model:value="codeForm.duration_minutes" :min="1" :max="60" placeholder="10" />
              </a-form-item>
              
              <a-form-item>
                <a-button type="primary" html-type="submit" :loading="generatingCode">
                  生成签退码
                </a-button>
              </a-form-item>
            </a-form>
            
            <!-- 显示生成的签退码 -->
            <div v-if="currentCheckoutCode" class="checkout-code-display">
              <a-alert
                :message="`签退码: ${currentCheckoutCode.session_code}`"
                :description="`有效期至: ${currentCheckoutCode.valid_until}`"
                type="success"
                show-icon
                closable
                @close="currentCheckoutCode = null"
              />
            </div>
          </div>

          <!-- 签到状态调整 -->
          <div class="status-adjustment-section">
            <h4>签到状态调整</h4>
            <a-button @click="showStatusModal" type="default">
              <EditOutlined />
              调整学生签到状态
            </a-button>
          </div>
        </a-space>
      </a-card>
    </div>

    <!-- 今日签到记录 -->
    <div class="today-records">
      <a-card title="今日签到记录">
        <a-table
          :columns="recordColumns"
          :data-source="todayRecords"
          :loading="loadingRecords"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'check_in_time'">
              {{ record.check_in_time ? formatTime(record.check_in_time) : '-' }}
            </template>
            <template v-if="column.key === 'check_out_time'">
              {{ record.check_out_time ? formatTime(record.check_out_time) : '-' }}
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 状态调整模态框 -->
    <a-modal
      v-model:open="statusModalVisible"
      title="调整签到状态"
      @ok="handleStatusAdjustment"
      @cancel="statusModalVisible = false"
      :confirm-loading="adjustingStatus"
    >
      <a-form ref="statusFormRef" :model="statusForm" layout="vertical">
        <a-form-item label="选择记录" name="attendance_id" :rules="[{ required: true, message: '请选择要调整的记录' }]">
          <a-select v-model:value="statusForm.attendance_id" placeholder="请选择签到记录">
            <a-select-option v-for="record in todayRecords" :key="record.id" :value="record.id">
              {{ record.student_name }} ({{ record.student_number }}) - {{ record.course_name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="新状态" name="status" :rules="[{ required: true, message: '请选择新状态' }]">
          <a-select v-model:value="statusForm.status" placeholder="请选择新状态">
            <a-select-option value="present">正常</a-select-option>
            <a-select-option value="late">迟到</a-select-option>
            <a-select-option value="absent">缺勤</a-select-option>
            <a-select-option value="truant">旷课</a-select-option>
            <a-select-option value="early_leave">早退</a-select-option>
            <a-select-option value="sick_leave">病假</a-select-option>
            <a-select-option value="personal_leave">事假</a-select-option>
            <a-select-option value="official_leave">公假</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="调整原因" name="reason">
          <a-textarea v-model:value="statusForm.reason" placeholder="请输入调整原因" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { CameraOutlined, EditOutlined } from '@ant-design/icons-vue'
import { authUtils } from '../utils/api'
import AttendanceCamera from './AttendanceCamera.vue'
import dayjs from 'dayjs'

export default {
  name: 'AttendanceCheck',
  components: {
    CameraOutlined,
    EditOutlined,
    AttendanceCamera
  },
  setup() {
    const checkingIn = ref(false)
    const checkingOut = ref(false)
    const generatingCode = ref(false)
    const loadingRecords = ref(false)
    const adjustingStatus = ref(false)
    const statusModalVisible = ref(false)
    const faceRecognitionModalVisible = ref(false)
    const faceRecognitionCheckoutModalVisible = ref(false)
    
    const availableCourses = ref([])
    const myCourses = ref([])
    const todayRecords = ref([])
    const currentCheckoutCode = ref(null)
    
    const statusFormRef = ref()

    const currentUser = computed(() => authUtils.getCurrentUser())
    const isTeacher = computed(() => {
      return currentUser.value && ['admin', 'teacher'].includes(currentUser.value.role)
    })

    const checkInForm = reactive({
      course_id: null,
      student_number: ''
    })

    const checkOutForm = reactive({
      student_number: '',
      session_code: ''
    })

    const codeForm = reactive({
      course_id: null,
      duration_minutes: 10
    })

    const statusForm = reactive({
      attendance_id: null,
      status: '',
      reason: ''
    })

    const recordColumns = [
      { title: '学生姓名', dataIndex: 'student_name', key: 'student_name' },
      { title: '学号', dataIndex: 'student_number', key: 'student_number' },
      { title: '课程', dataIndex: 'course_name', key: 'course_name' },
      { title: '签到时间', key: 'check_in_time' },
      { title: '签退时间', key: 'check_out_time' },
      { title: '状态', key: 'status' }
    ]

    const getDayName = (dayNumber) => {
      const days = ['', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
      return days[dayNumber] || ''
    }

    const getStatusColor = (status) => {
      const colors = {
        present: 'green',
        late: 'orange',
        absent: 'red',
        truant: 'red',
        early_leave: 'orange',
        sick_leave: 'blue',
        personal_leave: 'blue',
        official_leave: 'purple'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        present: '正常',
        late: '迟到',
        absent: '缺勤',
        truant: '旷课',
        early_leave: '早退',
        sick_leave: '病假',
        personal_leave: '事假',
        official_leave: '公假'
      }
      return texts[status] || status
    }

    const formatTime = (timeStr) => {
      return dayjs(timeStr).format('HH:mm:ss')
    }

    // 加载可用课程
    const loadAvailableCourses = async () => {
      try {
        const response = await fetch('/api/courses', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          availableCourses.value = data.courses.filter(course => course.is_active)
          
          if (isTeacher.value) {
            myCourses.value = data.courses.filter(course => 
              course.teacher_id === currentUser.value.id && course.is_active
            )
          }
        }
      } catch (error) {
        console.error('加载课程失败:', error)
      }
    }

    // 加载今日签到记录
    const loadTodayRecords = async () => {
      loadingRecords.value = true
      try {
        const today = dayjs().format('YYYY-MM-DD')
        const response = await fetch(`/api/attendance/records?start_date=${today}&end_date=${today}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          todayRecords.value = data.records
        }
      } catch (error) {
        console.error('加载签到记录失败:', error)
      } finally {
        loadingRecords.value = false
      }
    }

    // 开始人脸识别签到
    const startFaceRecognitionCheckIn = () => {
      if (!checkInForm.course_id || !checkInForm.student_number) {
        message.error('请先选择课程和输入学号')
        return
      }
      faceRecognitionModalVisible.value = true
    }

    // 开始人脸识别签退
    const startFaceRecognitionCheckOut = () => {
      if (!checkOutForm.session_code || !checkOutForm.student_number) {
        message.error('请先输入学号和签退码')
        return
      }
      faceRecognitionCheckoutModalVisible.value = true
    }

    // 处理人脸拍摄完成（签到）
    const onFaceCaptured = (data) => {
      console.log('人脸拍摄完成:', data)
    }

    // 处理人脸识别结果（签到）
    const onRecognitionResult = async (result) => {
      if (result.success) {
        await handleCheckIn()
        faceRecognitionModalVisible.value = false
      } else {
        message.error('人脸识别失败，请重试')
      }
    }

    // 处理人脸拍摄完成（签退）
    const onFaceCheckoutCaptured = (data) => {
      console.log('签退人脸拍摄完成:', data)
    }

    // 处理人脸识别结果（签退）
    const onCheckoutRecognitionResult = async (result) => {
      if (result.success) {
        await handleCheckOut()
        faceRecognitionCheckoutModalVisible.value = false
      } else {
        message.error('人脸识别失败，请重试')
      }
    }

    // 处理摄像头错误
    const onCameraError = (error) => {
      console.error('摄像头错误:', error)
      message.error('摄像头启动失败，请检查设备权限')
    }

    // 处理签到
    const handleCheckIn = async () => {
      checkingIn.value = true
      try {
        const response = await fetch('/api/attendance/check-in', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify(checkInForm)
        })

        const data = await response.json()
        
        if (response.ok) {
          message.success(`${data.student_name} 签到成功！状态：${getStatusText(data.status)}`)
          checkInForm.student_number = ''
          loadTodayRecords()
        } else {
          message.error(data.error || '签到失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        checkingIn.value = false
      }
    }

    // 处理签退
    const handleCheckOut = async () => {
      checkingOut.value = true
      try {
        const response = await fetch('/api/attendance/check-out', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify(checkOutForm)
        })

        const data = await response.json()
        
        if (response.ok) {
          message.success(`${data.student_name} 签退成功！`)
          checkOutForm.student_number = ''
          checkOutForm.session_code = ''
          loadTodayRecords()
        } else {
          message.error(data.error || '签退失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        checkingOut.value = false
      }
    }

    // 生成签退码
    const generateCheckoutCode = async () => {
      generatingCode.value = true
      try {
        const response = await fetch('/api/attendance/generate-checkout-code', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify(codeForm)
        })

        const data = await response.json()
        
        if (response.ok) {
          currentCheckoutCode.value = data
          message.success('签退码生成成功')
        } else {
          message.error(data.error || '生成签退码失败')
        }
      } catch (error) {
        message.error('网络错误')
      } finally {
        generatingCode.value = false
      }
    }

    // 显示状态调整模态框
    const showStatusModal = () => {
      statusModalVisible.value = true
    }

    // 处理状态调整
    const handleStatusAdjustment = async () => {
      try {
        await statusFormRef.value.validate()
        adjustingStatus.value = true

        const response = await fetch('/api/attendance/adjust-status', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify(statusForm)
        })

        if (response.ok) {
          message.success('签到状态调整成功')
          statusModalVisible.value = false
          loadTodayRecords()
          // 重置表单
          Object.assign(statusForm, {
            attendance_id: null,
            status: '',
            reason: ''
          })
        } else {
          const error = await response.json()
          message.error(error.error || '调整失败')
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        adjustingStatus.value = false
      }
    }

    onMounted(() => {
      loadAvailableCourses()
      loadTodayRecords()
    })

    return {
      checkingIn,
      checkingOut,
      generatingCode,
      loadingRecords,
      adjustingStatus,
      statusModalVisible,
      availableCourses,
      myCourses,
      todayRecords,
      currentCheckoutCode,
      statusFormRef,
      currentUser,
      isTeacher,
      checkInForm,
      checkOutForm,
      codeForm,
      statusForm,
      recordColumns,
      getDayName,
      getStatusColor,
      getStatusText,
      formatTime,
      handleCheckIn,
      handleCheckOut,
      generateCheckoutCode,
      showStatusModal,
      handleStatusAdjustment,
      startFaceRecognitionCheckIn,
      startFaceRecognitionCheckOut,
      onFaceCaptured,
      onRecognitionResult,
      onFaceCheckoutCaptured,
      onCheckoutRecognitionResult,
      onCameraError,
      faceRecognitionModalVisible,
      faceRecognitionCheckoutModalVisible
    }
  }
}
</script>

<style scoped>
/* 主容器样式 - 现代化设计 */
.attendance-check {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: calc(100vh - 64px);
  position: relative;
  overflow-x: hidden;
}

.attendance-check::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* 页面标题区域 */
.header {
  text-align: center;
  margin-bottom: 28px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 32px 24px;
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.header h2 {
  font-size: 32px;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 签到签退卡片区域 */
.check-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.check-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.check-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.check-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 教师功能区域 */
.teacher-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.teacher-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
}

.teacher-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}

.checkout-code-section,
.status-adjustment-section {
  padding: 24px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.checkout-code-section::before,
.status-adjustment-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.checkout-code-section h4,
.status-adjustment-section h4 {
  margin: 0 0 20px 0;
  color: #667eea;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: -0.3px;
}

.checkout-code-display {
  margin-top: 20px;
}

/* 今日记录区域 */
.today-records {
  margin-top: 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 28px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.today-records::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 20px 20px 0 0;
}

/* 卡片头部样式 - 现代化设计 */
:deep(.ant-card-head) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 24px 28px;
  position: relative;
  overflow: hidden;
}

:deep(.ant-card-head::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

:deep(.ant-card-head-title) {
  color: white;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -0.3px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

:deep(.ant-card-body) {
  padding: 28px;
  background: transparent;
}

/* 表格样式 - 现代化设计 */
:deep(.ant-table) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  font-weight: 700;
  color: #2c3e50;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
  font-size: 16px;
  padding: 20px 16px;
  text-align: center;
  letter-spacing: -0.2px;
}

:deep(.ant-table-tbody > tr > td) {
  font-size: 15px;
  padding: 18px 16px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  transform: scale(1.01);
}

:deep(.ant-table-tbody > tr:last-child > td) {
  border-bottom: none;
}

/* 按钮样式优化 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  height: 40px;
  font-size: 15px;
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 表单样式优化 */
:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
  font-size: 15px;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 8px;
  font-weight: 500;
  padding: 6px 12px;
  font-size: 13px;
  border: none;
}

/* 模态框样式优化 */
:deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

:deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

:deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

:deep(.ant-modal-close) {
  color: white;
}

:deep(.ant-modal-body) {
  padding: 24px;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-check {
    padding: 16px;
  }

  .check-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .header {
    padding: 20px;
    margin-bottom: 24px;
  }

  .header h2 {
    font-size: 24px;
  }

  :deep(.ant-card-body) {
    padding: 20px;
  }

  .checkout-code-section,
  .status-adjustment-section {
    padding: 16px;
  }

  .today-records {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .attendance-check {
    padding: 12px;
  }

  .header {
    padding: 16px;
  }

  .header h2 {
    font-size: 20px;
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }

  .checkout-code-section,
  .status-adjustment-section {
    padding: 12px;
  }

  .today-records {
    padding: 16px;
  }

  :deep(.ant-btn-primary) {
    height: 36px;
    font-size: 14px;
  }
}
</style>
